package app

import (
	"context"
	"errors"
	"os"
	"os/signal"
	"snapfile/pkg/transport"
	"sync"
	"syscall"

	"golang.org/x/sync/errgroup"
)

type App struct {
	ctx     context.Context
	cancel  context.CancelFunc
	sigs    []os.Signal
	servers []transport.Server
}

func New(opts ...Options) *App {
	opt := &option{
		ctx:     context.Background(),
		sigs:    []os.Signal{syscall.SIGTERM, syscall.SIGQUIT, syscall.SIGINT},
		servers: []transport.Server{},
	}
	for _, o := range opts {
		o(opt)
	}
	ctx, cancel := context.WithCancel(opt.ctx)
	return &App{
		ctx:     ctx,
		cancel:  cancel,
		sigs:    opt.sigs,
		servers: opt.servers,
	}
}

func (a *App) Run() error {
	eg, ctx := errgroup.WithContext(a.ctx)
	wg := sync.WaitGroup{}

	for _, srv := range a.servers {
		server := srv
		eg.Go(func() error {
			<-ctx.Done() // 等待停止信号
			stopCtx := a.ctx
			return server.Stop(stopCtx)
		})
		wg.Add(1)
		eg.Go(func() error {
			wg.Done()
			return server.Start(a.ctx)
		})
	}
	wg.Wait()

	c := make(chan os.Signal, 1)
	signal.Notify(c, a.sigs...)
	eg.Go(func() error {
		select {
		case <-ctx.Done():
			return nil
		case <-c:
			return a.Stop()
		}
	})
	if err := eg.Wait(); err != nil && !errors.Is(err, context.Canceled) {
		return err
	}
	return nil
}

func (a *App) Stop() (err error) {
	if a.cancel != nil {
		a.cancel()
	}
	return err
}
