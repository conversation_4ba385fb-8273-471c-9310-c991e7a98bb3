# 建议的开发命令

## 构建和运行
```bash
# 构建项目
go build -o snapfile.exe .

# 运行项目
go run . --help
go run . --ffmpeg-path "C:\path\to\ffmpeg.exe" --max-downloading-task 10

# 构建不同平台版本
make mac      # 构建 macOS 版本
make windows  # 构建 Windows 版本
make all      # 构建所有平台并打包
```

## 测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/stage/converter
go test ./pkg/shard

# 运行测试并显示详细输出
go test -v ./...

# 运行测试并生成覆盖率报告
go test -cover ./...
```

## 代码质量
```bash
# 格式化代码
go fmt ./...

# 静态分析
go vet ./...

# 下载和整理依赖
go mod tidy
go mod download
```

## 发布
```bash
# 使用 GoReleaser 创建快照版本
goreleaser release --snapshot

# 正式发布（需要 git tag）
make release
```

## Windows 系统工具命令
```cmd
# 文件操作
dir           # 列出目录内容
type file.txt # 查看文件内容
findstr "pattern" *.go  # 在文件中搜索模式

# Git 操作
git status
git add .
git commit -m "message"
git push

# 进程管理
tasklist      # 查看运行进程
taskkill /PID <pid>  # 终止进程
```