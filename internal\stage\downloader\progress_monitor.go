package downloader

import (
	"errors"
	"io"
	"log/slog"
	"snapfile/internal/event"
	"snapfile/pkg/progress"
	"sync"
	"sync/atomic"
	"time"
)

// 单个协程使用
type progressReader struct {
	reader io.ReadCloser
	report func(done int64)
}

func newProgressReader(reader io.ReadCloser, report func(done int64)) *progressReader {
	return &progressReader{
		reader: reader,
		report: report,
	}
}

func (s *progressReader) Read(buf []byte) (n int, err error) {
	n, err = s.reader.Read(buf)
	if err != nil && !errors.Is(err, io.EOF) {
		return n, event.ErrDownloadError().Wrap(err)
	}
	if n > 0 {
		if s.report != nil {
			s.report(int64(n))
		}
	}
	return n, err
}

func (s *progressReader) Close() error {
	err := s.reader.Close()
	if err != nil {
		slog.Warn("进度读取器关闭失败", "err", err)
		return event.ErrDownloadError().Wrap(err)
	}
	return nil
}

// 汇总多个协程的进度
type globalProgressMonitor struct {
	done         atomic.Int64
	total        int64
	_type        string
	one          sync.Once
	stop         chan struct{}
	callback     progress.ReportCallback
	lastTimeDone int64
}

func newGlobalProgressMonitor(total int64, callback progress.ReportCallback) progress.Monitor {
	return &globalProgressMonitor{
		stop:     make(chan struct{}),
		callback: callback,
		total:    total,
		_type:    "getBody",
	}
}

func (g *globalProgressMonitor) Start() {}

func (g *globalProgressMonitor) Report(done int64) {
	g.one.Do(func() {
		go func() {
			ticker := time.NewTicker(time.Second)
			defer ticker.Stop()
			for {
				select {
				case <-g.stop:
					ticker.Stop()
					//g.callback(g._type, g.total, g.done.Load()) // 最后一次上报
					return
				case <-ticker.C:
					currentDone := g.done.Load()
					speed := max(currentDone-g.lastTimeDone, 0)

					// 计算剩余时间
					var remainingTime int64 = 0
					if speed > 0 && currentDone < g.total {
						remainingTime = (g.total - currentDone) / speed
					}

					g.lastTimeDone = currentDone
					g.callback(g._type, g.total, g.done.Load(), speed, remainingTime)
				}
			}
		}()
	})
	g.done.Add(done)
}

func (g *globalProgressMonitor) Stop() {
	select { // 防止重复调用stop方法panic
	case <-g.stop:
	default:
		close(g.stop)
	}
}
