package prepare

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"path"
	"snapfile/internal/entity"
	"snapfile/pkg/httpx"
	"snapfile/pkg/tools"
	"strconv"
	"strings"
)

const (
	MediaTypeAudio     = "AUDIO"
	MediaTypeSubtitles = "SUBTITLES"
)

type Result struct {
	URL       *url.URL
	M3u8      *M3u8
	MediaType entity.FileType
	ExtMedias []*Result
}

// parseResolution 解析分辨率字符串，返回宽度和高度
func parseResolution(resolution string) (int, int, error) {
	if resolution == "" {
		return 0, 0, nil
	}
	parts := strings.Split(resolution, "x")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid resolution format: %s", resolution)
	}
	width, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, fmt.E<PERSON>rf("invalid width in resolution: %s", parts[0])
	}
	height, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, 0, fmt.E<PERSON><PERSON>("invalid height in resolution: %s", parts[1])
	}
	return width, height, nil
}

// selectHighestResolution 选择分辨率最高的流
func selectHighestResolution(playlists []*MasterPlaylist) *MasterPlaylist {
	if len(playlists) == 0 {
		return nil
	}

	var bestPlaylist *MasterPlaylist
	var maxPixels int

	for _, playlist := range playlists {
		width, height, err := parseResolution(playlist.Resolution)
		if err != nil {
			// 如果解析失败，跳过这个流
			continue
		}

		pixels := width * height
		if bestPlaylist == nil || pixels > maxPixels {
			bestPlaylist = playlist
			maxPixels = pixels
		}
	}

	// 如果没有找到有效的分辨率，返回第一个
	if bestPlaylist == nil {
		return playlists[0]
	}

	return bestPlaylist
}

// ParseFromURL 解析m3u8文件
func ParseFromURL(ctx context.Context, file entity.File) (*Result, error) {
	u, err := url.Parse(file.URL)
	if err != nil {
		return nil, err
	}

	// 下载并解析主 m3u8 文件
	m3u8, err := downloadAndParse(ctx, u, file.Headers)
	if err != nil {
		return nil, err
	}

	// 处理 Master Playlist（多码率播放列表）
	if len(m3u8.MasterPlaylist) > 0 {
		return parsePlaylist(ctx, m3u8, u, file)
	}

	// 普通媒体播放列表
	if len(m3u8.Segments) == 0 {
		return nil, errors.New("can not find any TS segment")
	}

	return &Result{
		URL:       u,
		M3u8:      m3u8,
		MediaType: entity.FileTypeVideo,
	}, nil
}

func downloadAndParse(ctx context.Context, u *url.URL, headers map[string]string) (*M3u8, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	resp, err := httpx.GetClient(tools.GetProxyFromContext(ctx)).Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	return parse(resp.Body)
}

func parsePlaylist(ctx context.Context, m3u8 *M3u8, base *url.URL, file entity.File) (*Result, error) {
	// 选择最佳视频流
	stream := selectHighestResolution(m3u8.MasterPlaylist)
	file.URL = ResolveURL(base, stream.URI)

	videoRes, err := ParseFromURL(ctx, file)
	if err != nil {
		return nil, err
	}
	videoURL, err := url.Parse(file.URL)
	if err != nil {
		return nil, err
	}
	res := &Result{
		MediaType: entity.FileTypeVideo,
		URL:       videoURL,
		M3u8:      videoRes.M3u8,
	}

	if len(m3u8.ExtMedias) != 0 {
		for _, media := range m3u8.ExtMedias {
			processMedia := func(mediaType, groupID string, defaultOnly bool, fileType entity.FileType) error {
				if media.Type == mediaType && (!defaultOnly || media.Default) && (groupID == "" || media.GroupID == groupID) {
					file.URL = ResolveURL(base, media.URI)
					parsed, err := ParseFromURL(ctx, file)
					if err != nil {
						slog.Warn("ExtMedia parse failed", "media", media.URI, "error", err)
						return err
					}
					res.ExtMedias = append(res.ExtMedias, &Result{
						URL:       parsed.URL,
						M3u8:      parsed.M3u8,
						MediaType: fileType,
					})
				}
				return nil
			}

			if err := processMedia(MediaTypeAudio, stream.GroupID, true, entity.FileTypeAudio); err != nil {
				return nil, err
			}
			if err := processMedia(MediaTypeSubtitles, "", true, entity.FileTypeSubtitle); err != nil {
				return nil, err
			}

		}
	}
	return res, nil
}

func ResolveURL(u *url.URL, p string) string {
	if strings.HasPrefix(p, "https://") || strings.HasPrefix(p, "http://") {
		return p
	}
	var baseURL string
	if strings.Index(p, "/") == 0 {
		baseURL = u.Scheme + "://" + u.Host
	} else {
		tU := u.String()
		baseURL = tU[0:strings.LastIndex(tU, "/")]
	}
	return baseURL + path.Join("/", p)
}

func GetSegmentURL(baseURL, relativePath string) (string, error) {
	if strings.HasPrefix(relativePath, "https://") || strings.HasPrefix(relativePath, "http://") {
		return relativePath, nil
	}
	// 解析基础 URL
	base, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("解析基础 URL 失败: %v", err)
	}

	if strings.HasPrefix(relativePath, "/") {
		return base.Scheme + "://" + base.Host + relativePath, nil
	}

	// 处理相对路径
	// 使用 path.Join 处理路径，确保正确解析 ../ 或直接文件名
	resolvedPath := path.Join(path.Dir(base.Path), relativePath)

	// 构造新的 URL
	result, err := url.Parse(resolvedPath)
	if err != nil {
		return "", fmt.Errorf("解析基础 URL 失败: %v", err)
	}
	result.Scheme = base.Scheme
	result.Host = base.Host
	return result.String(), nil
}
