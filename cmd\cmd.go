package cmd

import (
	"github.com/spf13/cobra"
	"log/slog"
	"os"
	"snapfile/internal/config"
	"snapfile/internal/handler"
	"snapfile/pkg/app"
)

var room cobra.Command

func init() {
	room = cobra.Command{
		Use:  "snapfile",
		RunE: run,
	}
	room.PersistentFlags().StringVar(&config.GlobalConfig.FFmpegPath, "ffmpeg-path", "ffmpeg", "FFmpeg path")
	room.PersistentFlags().StringVar(&config.GlobalConfig.FFprobePath, "ffprobe-path", "ffprobe", "FFprobe path")
	room.PersistentFlags().IntVar(&config.GlobalConfig.MaxDownloadTask, "max-downloading-task", 5, "The maximum number of simultaneous download tasks")
	room.PersistentFlags().StringVar(&config.GlobalConfig.LogLevel, "log-level", "info", "[debug, info, warn, error]")
}

func newApp() *app.App {
	return app.New(app.WithServers(handler.NewStdioServer()))
}

func run(_ *cobra.Command, _ []string) error {
	level := getLogLevel(config.GlobalConfig.LogLevel)
	loggerHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		AddSource: level == slog.LevelDebug,
		Level:     level,
	})
	logger := slog.New(&contextLogHandler{Handler: loggerHandler})
	slog.SetDefault(logger)
	return newApp().Run()
}

func Run() {
	if err := room.Execute(); err != nil {
		panic(err)
	}
}
