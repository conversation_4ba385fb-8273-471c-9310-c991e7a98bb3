package handler

import (
	"context"
	"snapfile/internal/event"
	"snapfile/internal/schema"
	"snapfile/internal/task"
	"snapfile/pkg/tools"
)

func DeleteTaskHandler(_ context.Context, e *event.Input) (*event.Output, error) {
	req := event.GetPayload[schema.DelTaskEventReq](e)
	err := tools.Validate(req)
	if err != nil {
		return nil, err
	}
	for _, id := range req.TaskIDs {
		task.DelTask(id)
	}
	return nil, nil
}
