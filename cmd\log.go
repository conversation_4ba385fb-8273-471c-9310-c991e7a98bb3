package cmd

import (
	"context"
	"log/slog"
	"snapfile/internal/constant"
	"snapfile/pkg/tools"
	"strings"
)

func getLogLevel(s string) slog.Level {
	switch strings.ToLower(s) {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

type contextLogHandler struct {
	slog.Handler
}

func (h *contextLogHandler) Handle(ctx context.Context, r slog.Record) error {
	if taskID := tools.GetTaskID(ctx); taskID != "" {
		r.Add(constant.LogTaskID<PERSON>ey, taskID)
	}
	return h.Handler.Handle(ctx, r)
}
