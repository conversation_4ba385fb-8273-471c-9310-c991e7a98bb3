package handler

import (
	"context"
	"snapfile/internal/event"
	"snapfile/internal/schema"
	"snapfile/internal/task"
	"snapfile/pkg/tools"
)

func UpdateMaxDownloadTaskHandler(_ context.Context, e *event.Input) (*event.Output, error) {
	req := event.GetPayload[schema.UpdateMaxDownloadTaskReq](e)
	if err := tools.Validate(req); err != nil {
		return nil, err
	}
	task.UpdateMaxDownloadTask(req.Limit)
	return nil, nil
}
