# Snapfile 项目概览

## 项目目的
Snapfile 是一个用 Go 语言开发的文件下载和转换工具，主要用于：
- 下载网络文件（特别是视频文件）
- 进行媒体格式转换
- 支持断点续传和并发下载
- 支持 M3U8 直播流下载和录制

## 技术栈
- **语言**: Go 1.24
- **主要依赖**:
  - `github.com/spf13/cobra` - 命令行界面
  - `github.com/gabriel-vasile/mimetype` - MIME 类型检测
  - `github.com/go-playground/validator/v10` - 数据验证
  - `github.com/sunshineplan/imgconv` - 图像转换
  - `golang.org/x/sync` - 并发控制
- **外部工具**: FFmpeg/FFprobe（用于媒体处理）

## 核心架构
采用分阶段处理架构：
1. **准备阶段** (prepare) - URL解析、文件信息获取、分片计算
2. **下载阶段** (downloader) - 并发下载、断点续传
3. **转换阶段** (converter) - FFmpeg格式转换
4. **移动阶段** (move) - 文件最终移动

## 项目结构
- `cmd/` - 命令行接口和应用启动
- `internal/` - 核心业务逻辑（按模块划分）
- `pkg/` - 可复用的工具包
- `main.go` - 程序入口点