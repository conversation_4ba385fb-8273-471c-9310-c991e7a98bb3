package converter

import (
	"context"
	"os"
	"snapfile/internal/entity"
	"testing"
)

func TestHandler(t *testing.T) {
	type task struct {
		name string
		task *entity.Task
	}
	tasks := []*task{
		{
			name: "音频转换为mp4视频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "123",
					},
				},

				OutputType:        "video",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "音频转换为mkv视频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "123",
					},
				},

				OutputType:        "video",
				OutputVideoFormat: "mkv",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "音频转换为mp3音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "123",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "音频转换为ogg音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "123",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "ogg",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "音频转换为m4a音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "123",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "m4a",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "视频转换为mp3音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "视频转换为ogg音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "ogg",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "视频转换为m4a音频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
				},

				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "m4a",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "mp4视频转换为mp4视频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
				},
				OutputType:        "video",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "mp4视频转换为mkv视频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
				},
				OutputType:        "video",
				OutputVideoFormat: "mkv",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "mp4视频+mp3音频转换为mp4视频",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
					{
						URL: "123",
					},
				},
				OutputType:        "video",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "mp4视频+mp3音频转换为mp3",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
					{
						URL: "123",
					},
				},
				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "mp4视频+mp3音频+字幕转换为mp3",
			task: &entity.Task{
				ID:        "123",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123",
				Files: []entity.File{
					{
						URL: "321",
					},
					{
						URL: "123",
					},
					{
						URL: "234",
					},
				},
				OutputType:        "audio",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
		{
			name: "flv下载",
			task: &entity.Task{
				ID:        "123dd",
				Name:      "测试文件",
				OutputDir: "",
				TempDir:   ".snapfile/123dd",
				Files: []entity.File{
					{
						URL: "234",
					},
				},
				OutputType:        "video",
				OutputVideoFormat: "mp4",
				OutputAudioFormat: "mp3",
				DownloadProgressCallback: func(stage string, total int64, done int64) {
					t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
				},
			},
		},
	}

	for _, t2 := range tasks {
		if t2.name != "flv下载" {
			continue
		}
		t.Run(t2.name, func(t *testing.T) {
			err := Handler(context.Background(), t2.task)
			if err != nil {
				t.Error(err)
			}
		})
		os.RemoveAll(".snapfile/123/converted")
	}
}
