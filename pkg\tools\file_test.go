package tools

import (
	"fmt"
	"testing"
)

func TestRemoveIllegalFilenameChars(t *testing.T) {
	var illegalNames = []string{
		"bad<name>",
		"report:final?",
		"quote\"me\"",
		"path\\to\\file",
		"star*file",
		"null\x00file",
		"control\x1Fname",
		"<>",
		":\"/\\|?*",
		"trailingdot.",
		"trailingspace ",
		"CON",
		"PRN",
		"not/slash",
		"colon:name",
		"fun😂file?*",
		"emoji🚫:name/",
		"🚀launch?",
		"bad<:/\\*>name🚨",
	}
	for _, name := range illegalNames {
		res := RemoveIllegalFilenameChars(name)
		fmt.Println(res)
	}

}

func Test_addIndex(t *testing.T) {
	name := AddIndex("C:\\Users\\<USER>\\Desktop\\snapfile\\internal\\stage\\prepare\\file.txt", 1)
	fmt.Println(name)
}

func TestGetConvertedAllFile(t *testing.T) {
	files := GetConvertedAllFile("C:\\Users\\<USER>\\Desktop\\snapfile\\internal\\stage\\prepare")
	fmt.Println(files)
}

func TestHasDuplicateFile(t *testing.T) {
	file := HasDuplicateFile("C:\\Users\\<USER>\\Desktop\\snapfile\\test\\", "tast.go", 0)
	fmt.Println(file)
	file = HasDuplicateFile("C:\\Users\\<USER>\\Desktop\\snapfile\\test\\", "tast.go", 1)
	fmt.Println(file)
	file = HasDuplicateFile("C:\\Users\\<USER>\\Desktop\\snapfile\\test\\", "tast.go", 2)
	fmt.Println(file)
}

func TestTruncateFileName(t *testing.T) {
	tests := []struct {
		dir      string
		filename string
		expected string
	}{
		// 测试：文件名长度为 255 个字符
		{"C:\\Users\\<USER>\\Documents", string(make([]rune, 255)), string(make([]rune, 255))},
		// 测试：文件名长度为 256 个字符
		{"C:\\Users\\<USER>\\Documents", string(make([]rune, 256)), string(make([]rune, 255))},
		// 测试：文件名包含特殊字符
		{"C:\\Users\\<USER>\\Documents", "file<name>.txt", "file<name>.txt"},
		// 测试：文件名包含 Emoji
		{"C:\\Users\\<USER>\\Documents", "file🚀name.txt", "file🚀name.txt"},
		// 测试：文件名为空
		{"C:\\Users\\<USER>\\Documents", "", ""},
		// 测试：目录路径为空
		{"", "file.txt", "file.txt"},
	}

	for _, tt := range tests {
		t.Run(tt.filename, func(t *testing.T) {
			got := TruncateFileName(tt.filename, tt.dir)
			fmt.Println(len(got))
			fmt.Println(got)
		})
	}
}
