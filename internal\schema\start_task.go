package schema

import (
	"snapfile/internal/entity"
)

// StartTaskEventReq 开始任务事件请求结构体
type StartTaskEventReq struct {
	TaskID            string              `validate:"required" err:"任务id不能为空" json:"taskID,omitempty"`
	Name              string              `validate:"required" err:"任务名称不能为空" json:"name,omitempty"`        // 任务名称
	OutputDir         string              `validate:"required" err:"最终输出目录不能为空" json:"outputDir,omitempty"` // 最终输出目录
	TempDir           string              `json:"tempDir"  err:"临时目录不能为空" json:"tempDir"`                   // 临时目录
	Files             []File              `validate:"required" err:"待下载文件不能为空" json:"files"`                // 下载任务
	OutputType        string              `validate:"required" err:"输出类型不能为空" json:"outputType"`            // 输出类型, 视频或者音频
	OutputVideoFormat entity.OutputFormat `validate:"required" err:"视频默认输出格式不能为空" json:"outputVideoFormat"` // 输出视频格式
	OutputAudioFormat entity.OutputFormat `validate:"required" err:"音频默认输出格式不能为空" json:"outputAudioFormat"` // 输出音频格式
	Proxy             string              `validate:"required" err:"代理选项不能为空" json:"proxy"`                 // 代理地址，direct、system、custom(即http、sock)
	EmbeddedSubtitle  bool                `json:"embeddedSubtitle"`                                         // 字幕是否内嵌
}

type File struct {
	URL              string            `validate:"required" err:"下载链接不能为空" json:"url"` // 下载链接
	Header           map[string]string `json:"header"`                                 // 请求头
	Language         string            `json:"language"`                               // 字幕语言
	OptionalDownload bool              `json:"optionalDownload"`                       // 下载出现错误可忽略
}
