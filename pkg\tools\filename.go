package tools

import (
	"fmt"
	"path"
	"regexp"
	"strings"
)

func AddMark(target, mark string) string {
	if mark == "" {
		return target
	}
	if !strings.HasPrefix(mark, ".") {
		mark = "." + mark
	}
	ext := path.Ext(target)
	target = strings.TrimSuffix(target, ext)
	return fmt.Sprintf("%s%s%s", target, mark, ext)
}

func SetLanguage(filepath, language string) string {
	ext := path.Ext(filepath)
	filepath = strings.TrimSuffix(filepath, ext)
	return fmt.Sprintf("%s{language{%s}}%s", filepath, language, ext)
}

func GetLanguage(filepath string) string {
	re := regexp.MustCompile(`\{language\{(.*?)}}`)
	match := re.FindStringSubmatch(filepath)
	if len(match) >= 2 {
		return match[1]
	}
	return ""
}
