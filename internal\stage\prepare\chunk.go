package prepare

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"path/filepath"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/pkg/hash"
	"snapfile/pkg/httpx"
	m3u8 "snapfile/pkg/m3u8"
	"snapfile/pkg/shard"
	"snapfile/pkg/tools"
	"strconv"
	"strings"
	"sync/atomic"

	"golang.org/x/sync/errgroup"
)

type Chunk<PERSON>and<PERSON> func(ctx context.Context, task *entity.Task, file entity.File) ([]entity.File, error)

func GetChunkHandler(_type entity.URLType) ChunkHandler {
	switch _type {
	case entity.URLTypeM3u8:
		return m3u8Chunk
	default:
		return generalChunk
	}
}

// 获取分片名称
func getPartFilepath(tempDir, url string, index int) string {
	return filepath.Join(tempDir, constant.DownloadOutputDir, hash.MD5([]byte(url)), fmt.Sprintf("%d%s", index, constant.ChunkPartExtension))
}

func generalChunk(ctx context.Context, task *entity.Task, file entity.File) ([]entity.File, error) {
	r, err := http.NewRequestWithContext(ctx, http.MethodGet, file.URL, nil)
	if err != nil {
		slog.WarnContext(ctx, "创建HTTP请求失败", "err", err, "url", file.URL)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	for k, v := range file.Headers {
		r.Header.Set(k, v)
	}
	resp, err := httpx.GetClient(tools.GetProxyFromContext(ctx)).Do(r)
	if err != nil {
		slog.WarnContext(ctx, "执行HTTP请求失败", "err", err, "url", file.URL)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	_ = resp.Body.Close() // 立马关掉下载流，防止持续占用
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusForbidden {
			return nil, event.ErrHttpStatusForbiddenError().Wrap(err)
		}
		slog.WarnContext(ctx, "HTTP响应状态码错误", "status", resp.Status, "statusCode", resp.StatusCode, "url", file.URL)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	var chunks []entity.Chunk
	length, _ := strconv.ParseInt(resp.Header.Get("Content-Length"), 10, 64)
	file.Size = length
	// 保证最少有一片
	if length == 0 {
		chunks = []entity.Chunk{
			{},
		}
		if strings.Contains(resp.Header.Get("Content-Type"), "video") {
			task.Live = true
		}
	} else if resp.Header.Get("Accept-Ranges") != "bytes" {
		chunks = []entity.Chunk{
			{
				Length: length,
			},
		}
	} else {
		size := shard.AutoChunkSize(length)
		var err error
		shardChunks, err := shard.SplitBySize(length, size)
		if err != nil {
			slog.WarnContext(ctx, "分片拆分失败", "err", err, "length", length, "size", size)
			return nil, event.ErrPrepareError().Wrap(err)
		}
		for _, chunk := range shardChunks {
			chunks = append(chunks, entity.Chunk{
				Index:  chunk.Index,
				Offset: chunk.Offset,
				Length: chunk.Length,
			})
		}
	}

	for i, it := range chunks {
		chunks[i].OutputFilePath = getPartFilepath(task.TempDir, file.URL, it.Index)
		chunks[i].DownloadURL = file.URL
	}
	file.Chunks = chunks
	return []entity.File{file}, nil
}

func m3u8Chunk(ctx context.Context, task *entity.Task, file entity.File) ([]entity.File, error) {
	result, err := m3u8.ParseFromURL(ctx, file)
	if err != nil {
		slog.WarnContext(ctx, "解析M3U8文件失败", "err", err, "url", file.URL)
		return nil, event.ErrParseError().Wrap(err)
	}
	if !result.M3u8.EndList {
		task.Live = true
		return []entity.File{file}, nil
	}

	var files []entity.File

	addFile := func(url string, mediaType entity.FileType, m3u8 *m3u8.M3u8) error {
		chunks, err := getM3u8Chunks(url, task.TempDir, m3u8)
		if err != nil {
			return err
		}
		files = append(files, entity.File{
			URL:     url,
			URLType: entity.URLType(mediaType),
			Chunks:  chunks,
		})
		return nil
	}

	// 主视频文件
	if err := addFile(result.URL.String(), entity.FileTypeVideo, result.M3u8); err != nil {
		return nil, err
	}

	// 扩展媒体文件（音频/字幕）
	for _, media := range result.ExtMedias {
		if err := addFile(media.URL.String(), media.MediaType, media.M3u8); err != nil {
			return nil, err
		}
	}

	// 获取大小
	for i, file := range files {
		size, err := getAllChunkSizeByHead(ctx, file)
		if err != nil {
			slog.WarnContext(ctx, "获取分片大小失败", "err", err, "url", file.URL)
			return nil, event.ErrPrepareError().Wrap(err)
		}
		files[i].Size = size
	}

	return files, nil
}

func getAllChunkSizeByHead(ctx context.Context, file entity.File) (int64, error) {
	size := atomic.Int64{}
	wg, ctx := errgroup.WithContext(ctx)
	wg.SetLimit(constant.MaxDownloadConcurrent)
	for _, chunk := range file.Chunks {
		wg.Go(func() error {
			r, err := http.NewRequestWithContext(ctx, http.MethodHead, chunk.DownloadURL, nil)
			if err != nil {
				return err
			}
			resp, err := httpx.GetClient(tools.GetProxyFromContext(ctx)).Do(r)
			if err != nil {
				return err
			}
			defer resp.Body.Close()
			if resp.StatusCode != http.StatusOK { // head 请求失败后，使用get请求获取
				r.Method = http.MethodGet
				resp, err = httpx.GetClient(tools.GetProxyFromContext(ctx)).Do(r)
				if err != nil {
					return err
				}
				_ = resp.Body.Close() // 立马关掉下载流，防止持续占用
				if resp.StatusCode != http.StatusOK {
					return fmt.Errorf("http status code: %d", resp.StatusCode)
				}
			}
			length, _ := strconv.ParseInt(resp.Header.Get("Content-Length"), 10, 64)
			size.Add(length)
			return nil
		})
	}
	if err := wg.Wait(); err != nil {
		return 0, err
	}
	return size.Load(), nil
}

func getM3u8Chunks(baseURL, tempDir string, m3u8Data *m3u8.M3u8) ([]entity.Chunk, error) {
	chunks := make([]entity.Chunk, 0, len(m3u8Data.Segments))
	if m3u8Data.MapURI != "" {
		mapURL, err := m3u8.GetSegmentURL(baseURL, m3u8Data.MapURI)
		if err != nil {
			slog.Warn("获取MAP URL失败", "err", err, "url", baseURL)
			return nil, event.ErrPrepareError().Wrap(err)
		}
		chunks = append(chunks, entity.Chunk{
			Offset:         int64(m3u8Data.Offset),
			Length:         int64(m3u8Data.Length),
			DownloadURL:    mapURL,
			OutputFilePath: getPartFilepath(tempDir, baseURL, 0),
		})
	}
	for i, s := range m3u8Data.Segments {
		downloadURL, err := m3u8.GetSegmentURL(baseURL, s.URI)
		if err != nil {
			slog.Warn("获取SEGMENT URL失败", "err", err, "url", baseURL)
			return nil, event.ErrPrepareError().Wrap(err)
		}
		outputFilePath := getPartFilepath(tempDir, baseURL, i+1)
		chunks = append(chunks, entity.Chunk{
			Offset:         int64(s.Offset),
			Length:         int64(s.Length),
			DownloadURL:    downloadURL,
			OutputFilePath: outputFilePath,
		})
	}
	return chunks, nil
}
