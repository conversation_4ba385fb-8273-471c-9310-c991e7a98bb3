module snapfile

go 1.24

toolchain go1.24.4

require (
	github.com/gabriel-vasile/mimetype v1.4.9
	github.com/go-playground/validator/v10 v10.26.0
	github.com/mattn/go-ieproxy v0.0.12
	github.com/spf13/cobra v1.9.1
	github.com/sunshineplan/imgconv v1.1.14
	golang.org/x/sync v0.16.0
)

require (
	github.com/HugoSmits86/nativewebp v1.2.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/hhrutter/lzw v1.0.0 // indirect
	github.com/hhrutter/pkcs7 v0.2.0 // indirect
	github.com/hhrutter/tiff v1.0.2 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/pdfcpu/pdfcpu v0.11.0 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	github.com/sunshineplan/pdf v1.0.8 // indirect
	golang.org/x/crypto v0.40.0 // indirect
	golang.org/x/image v0.29.0 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/sys v0.34.0 // indirect
	golang.org/x/text v0.27.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
