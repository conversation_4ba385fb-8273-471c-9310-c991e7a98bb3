package move

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path"
	"path/filepath"
	"slices"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/pkg/tools"
	"strconv"
	"strings"
	"time"
)

func Handler(ctx context.Context, task *entity.Task) (err error) {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	exportDir := task.OutputDir
	if err := os.MkdirAll(exportDir, os.ModePerm); err != nil {
		slog.WarnContext(ctx, "创建导出目录失败", "err", err)
		return event.ErrMoveError().Wrap(err)
	}
	dirs, err := os.ReadDir(filepath.Join(task.TempDir, constant.ConvertedOutputDir))
	if err != nil {
		slog.WarnContext(ctx, "读取转换目录失败", "err", err)
		return event.ErrMoveError().Wrap(err)
	}
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	now := time.Now()
	for _, dir := range dirs {
		pendingMoveFullPath := filepath.Join(task.TempDir, constant.ConvertedOutputDir, dir.Name())
		filename := task.Name
		// 获取合法的文件名
		filename = getMovedFilename(filename, exportDir)
		if !dir.IsDir() {
			filename = filename + filepath.Ext(dir.Name())
		}
		if tools.GetLanguage(pendingMoveFullPath) != "" {
			filename = tools.AddMark(filename, tools.GetLanguage(pendingMoveFullPath))
		}
		if task.Live { // 如果是直播，在末尾加入时间后缀
			filename = addTime(filename, now)
		}
		var index = 0
		for tools.HasDuplicateFile(exportDir, filename, index) {
			index++
		}
		if index > 0 {
			filename = filepath.Base(tools.AddIndex(filename, index))
		}
		outputFilePath := filepath.Join(exportDir, filename)
		slog.DebugContext(ctx, "移动文件", "pending_move_file_path", pendingMoveFullPath, "output_file_path", outputFilePath)
		task.FinalizedFilePaths = append(task.FinalizedFilePaths, outputFilePath)
		if err := os.Rename(pendingMoveFullPath, outputFilePath); err != nil {
			slog.WarnContext(ctx, "移动文件失败", "err", err)
			return event.ErrMoveError().Wrap(err)
		}
	}
	err = os.RemoveAll(task.TempDir)
	if err != nil {
		slog.WarnContext(ctx, "删除临时文件失败", "err", err)
	}
	slices.SortStableFunc(task.FinalizedFilePaths, func(a, b string) int {
		if slices.Contains([]string{".mp4", ".mkv"}, path.Ext(a)) {
			return -2
		}
		if slices.Contains([]string{".mp3", ".ogg", ".m4a"}, path.Ext(a)) {
			return -1
		}
		return 1
	})
	return nil
}

func addTime(filename string, now time.Time) string {
	ext := path.Ext(filename)
	filename = strings.TrimSuffix(filename, ext)
	nowStr := now.Format("2006-01-0215:04")
	return fmt.Sprintf("%s_%s%s", filename, nowStr, ext)
}

func getMovedFilename(filename, exportDir string) string {
	filename = tools.RemoveIllegalFilenameChars(filename)
	// 判断非空
	if filename == "" {
		filename = strconv.FormatInt(time.Now().UnixMicro(), 64) // 采用时间戳命名
	}

	// 对超过的文件名做截断，已考虑加序号后的长度，截断做了冗余处理
	// 不考虑导出目录名过长的问题，默认目录+文件名在260字符以内
	return tools.TruncateFileName(filename, exportDir)
}
