package tools

import (
	"context"
	"snapfile/internal/constant"
	"sync"
)

type contextStoreKey struct{}

func SetValue(ctx context.Context, key any, value any) context.Context {
	val := ctx.Value(contextStoreKey{})
	m, ok := val.(*sync.Map)
	if !ok {
		m = &sync.Map{}
	}
	m.Store(key, value)
	return context.WithValue(ctx, contextStoreKey{}, m)
}

func GetValue(ctx context.Context, key any) any {
	val := ctx.Value(contextStoreKey{})
	m, ok := val.(*sync.Map)
	if !ok {
		return nil
	}
	value, ok := m.Load(key)
	if !ok {
		return nil
	}
	return value
}

func GetProxyFromContext(ctx context.Context) string {
	val := GetValue(ctx, constant.CtxProxyKey)
	if val == nil {
		return ""
	}
	proxy, ok := val.(string)
	if !ok {
		return ""
	}
	return proxy
}

type contextTaskIDKey struct{}

func SetTaskID(ctx context.Context, taskID string) context.Context {
	return SetValue(ctx, contextTaskIDKey{}, taskID)
}

func GetTaskID(ctx context.Context) string {
	val := GetValue(ctx, contextTaskIDKey{})
	if val == nil {
		return ""
	}
	taskID, ok := val.(string)
	if !ok {
		return ""
	}
	return taskID
}
