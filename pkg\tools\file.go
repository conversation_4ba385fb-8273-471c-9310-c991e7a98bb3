package tools

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

const (
	fileNameMaxLength = 50  // windows文件名最长255字节
	filePathMaxLength = 260 // windows路径最长260字节
	availLength       = 15  // 预留长度 用于添加后缀 路径分隔符做冗余 \\(10).m3u8
)

// RemoveIllegalFilenameChars 删除文件名中所有系统不支持的字符
func RemoveIllegalFilenameChars(name string) string {
	name = strings.TrimPrefix(name, " ")
	name = strings.TrimSuffix(name, " ")
	// 匹配 Windows 的非法字符 < > : " / \ | ? *、控制字符（0–31）、以及 macOS/Linux 的 null (\x00)
	re := regexp.MustCompile(`[\x00-\x1F<>:"/\\|?*\x00\s]`)
	cleaned := re.ReplaceAllString(name, "")

	// Windows 不允许结尾是空格或点
	cleaned = strings.TrimRight(cleaned, " .")

	return cleaned
}

// TruncateFileName 根据目录限制+平台原则截断
func TruncateFileName(name, dir string) string {
	// 先根据最大组件长度截断
	runes := []rune(name)
	if len(runes) > fileNameMaxLength {
		runes = runes[:fileNameMaxLength]
	}
	base := string(runes)

	// 再根据完整路径长度限制 Windows 默认 MAX_PATH
	full := filepath.Join(dir, base)
	if len(full) > filePathMaxLength {
		// 预留路径长度
		avail := filePathMaxLength - len(dir) - len(string(filepath.Separator))
		avail = avail - availLength // 用于添加后缀 路径分隔符做冗余 .m3u8(5*2)
		runes = []rune(base)
		if len(runes) > avail {
			runes = runes[:avail]
		}
		base = string(runes)
	}
	return base
}

// AddIndex 在基本文件名（含扩展名）后添加序号；index 为 0 时返回原名
func AddIndex(filename string, index int) string {
	if index <= 0 {
		return filename
	}
	ext := filepath.Ext(filename)
	base := filename[:len(filename)-len(ext)]
	return fmt.Sprintf("%s(%d)%s", base, index, ext)
}

// GetConvertedAllFile 获取指定目录下的所有文件（递归），返回完整路径切片
func GetConvertedAllFile(exportDir string) []string {
	var files []string
	filepath.WalkDir(exportDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			// 如果访问出错（权限或 IO 等），跳过该条目
			return nil
		}
		if !d.IsDir() {
			files = append(files, path)
		}
		return nil
	})
	return files
}

// HasDuplicateFile 判断目标目录是否存在重名文件（原名或带索引版本）
func HasDuplicateFile(targetDir, filename string, index int) bool {
	if index > 0 {
		filename = AddIndex(filename, index)
	}
	// 拼接完整路径
	full := filepath.Join(targetDir, filename)
	return exists(full)
}

// exists 检查路径是否存在并且不是路径错误
func exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil || !errors.Is(err, os.ErrNotExist)
}
