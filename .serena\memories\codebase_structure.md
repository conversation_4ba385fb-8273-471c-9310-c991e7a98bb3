# 代码库结构详解

## 顶层目录
```
snapfile/
├── cmd/                 # 命令行接口
├── internal/            # 内部业务逻辑
├── pkg/                 # 可复用工具包
├── main.go             # 程序入口
├── go.mod              # Go 模块定义
├── Makefile            # 构建脚本
└── README.md           # 项目文档
```

## internal/ 目录（核心业务逻辑）
```
internal/
├── config/             # 全局配置管理
├── constant/           # 项目常量定义
├── entity/             # 数据模型（Task, File, Chunk等）
├── event/              # 事件系统（错误处理、事件代码）
├── handler/            # 事件处理器（业务逻辑入口）
├── schema/             # 请求/响应数据结构
├── stage/              # 分阶段处理核心
│   ├── converter/      # 转换阶段（FFmpeg）
│   ├── downloader/     # 下载阶段（并发下载）
│   ├── move/           # 移动阶段（文件整理）
│   └── prepare/        # 准备阶段（URL解析、分片）
├── store/              # 数据存储
└── task/               # 任务管理
```

## pkg/ 目录（工具包）
```
pkg/
├── app/                # 应用框架
├── ffmpeg/             # FFmpeg 工具封装
├── hash/               # 哈希计算工具
├── httpx/              # HTTP 客户端扩展
├── m3u8/               # M3U8 解析工具
├── progress/           # 进度跟踪
├── semaphore/          # 信号量控制
├── shard/              # 文件分片处理
├── tools/              # 通用工具函数
└── transport/          # 传输层抽象
```

## 关键文件说明
- `main.go` - 程序入口，调用 cmd.Run()
- `cmd/cmd.go` - 命令行参数解析和应用初始化
- `internal/stage/runner.go` - 阶段执行器，定义处理流水线
- `internal/handler/start_task.go` - 任务启动的核心业务逻辑