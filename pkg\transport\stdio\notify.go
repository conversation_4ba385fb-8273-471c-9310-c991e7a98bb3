package stdio

import (
	"bufio"
	"encoding/json"
	"os"
	"snapfile/internal/event"
	"sync"
)

var (
	notifyWriter    *bufio.Writer
	notifyChan      chan *event.Output
	notifyInitOnce  sync.Once
	notifyStartOnce sync.Once
)

func initNotify() {
	notifyInitOnce.Do(func() {
		notifyWriter = bufio.NewWriter(os.Stdout)
		notifyChan = make(chan *event.Output, 1024)
	})
}

func startNotify() {
	notifyStartOnce.Do(func() {
		go func() {
			defer close(notifyChan)
			for msg := range notify<PERSON>han {
				data, err := json.Marshal(msg)
				if err != nil {
					continue
				}
				data = append(data, '\n')
				_, err = notifyWriter.Write(data)
				if err != nil {
					continue
				}
				err = notifyWriter.Flush()
				if err != nil {
					continue
				}
			}
		}()
	})
}

func Post(msg *event.Output) {
	notifyChan <- msg
}
