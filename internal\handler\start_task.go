package handler

import (
	"context"
	"maps"
	"path/filepath"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/internal/schema"
	"snapfile/internal/store"
	"snapfile/internal/task"
	"snapfile/pkg/diskerror"
	"snapfile/pkg/tools"
	"strings"
)

func StartTaskHandler(_ context.Context, e *event.Input) (*event.Output, error) {
	req := event.GetPayload[schema.StartTaskEventReq](e)

	err := tools.Validate(req)
	if err != nil {
		return nil, err
	}
	if len(req.Files) == 0 {
		return event.NewErrOutput(&event.Error{
			Code:    event.CodeParamInvalid,
			Message: event.CodeParamInvalid.Detail(),
		}, event.WithTaskID(req.TaskID)), nil
	}

	taskCtx := task.Get(req.TaskID)
	if taskCtx != nil {
		return nil, event.ErrTaskAlreadyStarted()
	}

	tempDir := filepath.Join(req.TempDir, req.TaskID)

	taskData, err := store.Get(tempDir, req.TaskID)
	if err != nil {
		if diskerror.IsDiskFull(err) {
			return event.NewErrOutput(event.ErrDiskFullError(), event.WithTaskID(req.TaskID)), nil
		} else if diskerror.IsPermission(err) {
			return event.NewErrOutput(event.ErrOSPermissionDeniedError(), event.WithTaskID(req.TaskID)), nil
		}
		return nil, err
	}
	if taskData == nil {
		taskData = &entity.Task{
			ID:                req.TaskID,
			Name:              req.Name,
			OutputDir:         req.OutputDir,
			OutputType:        req.OutputType,
			OutputVideoFormat: req.OutputVideoFormat,
			OutputAudioFormat: req.OutputAudioFormat,
			EmbeddedSubtitle:  req.EmbeddedSubtitle,
			Proxy:             req.Proxy,
			TempDir:           tempDir,
		}
		for _, file := range req.Files {
			maps.DeleteFunc(file.Header, func(k string, v string) bool {
				return strings.ToLower(k) == "accept-encoding" // 使用go自动解压
			})
			taskData.Files = append(taskData.Files, entity.File{
				URL:              file.URL,
				Headers:          file.Header,
				Language:         file.Language,
				OptionalDownload: file.OptionalDownload,
			})
		}
	}
	task.AddTask(req.TaskID, *taskData)
	return event.NewOutput(event.CodeTaskStarted, event.H{"taskID": req.TaskID}), nil
}
