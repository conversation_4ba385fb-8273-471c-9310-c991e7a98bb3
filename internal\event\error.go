package event

import "fmt"

type Error struct {
	cause     error
	retryable bool
	Code      Code   `json:"code"`
	Message   string `json:"message"`
}

func (e *Error) Error() string {
	if e.cause == nil {
		return e.Message
	}
	return fmt.Errorf("%s: %w", e.Message, e.cause).Error()
}

func (e *Error) Wrap(err error) *Error {
	return &Error{
		Code:    e.Code,
		Message: e.Message,
		cause:   err,
	}
}

func (e *Error) NotRetryable() *Error {
	e.retryable = false
	return e
}

func (e *Error) Retryable() *Error {
	e.retryable = true
	return e
}

func (e *Error) Unwrap() error {
	return e.cause
}

func ErrUnknownError() *Error {
	return &Error{
		Code:    CodeUnknownError,
		Message: CodeUnknownError.Detail(),
	}
}

func ErrUnknownEvent() *Error {
	return &Error{
		Code:    CodeUnknownEvent,
		Message: CodeUnknownEvent.Detail(),
	}
}

func ErrTaskAlreadyStarted() *Error {
	return &Error{
		Code:      CodeTaskAlreadyStarted,
		Message:   CodeTaskAlreadyStarted.Detail(),
		retryable: true,
	}
}

func ErrPrepareError() *Error {
	return &Error{
		Code:      CodePrepareError,
		Message:   CodePrepareError.Detail(),
		retryable: true,
	}
}

func ErrParseError() *Error {
	return &Error{
		Code:      CodeParseError,
		Message:   CodeParseError.Detail(),
		retryable: true,
	}
}

func ErrHttpStatusForbiddenError() *Error {
	return &Error{
		Code:    CodeHttpStatusForbiddenError,
		Message: CodeHttpStatusForbiddenError.Detail(),
	}
}

func ErrMoveError() *Error {
	return &Error{
		Code:      CodeMoveError,
		Message:   CodeMoveError.Detail(),
		retryable: true,
	}
}

func ErrDownloadError() *Error {
	return &Error{
		Code:      CodeDownloadError,
		Message:   CodeDownloadError.Detail(),
		retryable: true,
	}
}

func ErrConvertError() *Error {
	return &Error{
		Code:      CodeConvertError,
		Message:   CodeConvertError.Detail(),
		retryable: true,
	}
}

func ErrDiskFullError() *Error {
	return &Error{
		Code:      CodeDiskFull,
		Message:   CodeDiskFull.Detail(),
		retryable: true,
	}
}

func ErrOSPermissionDeniedError() *Error {
	return &Error{
		Code:      CodeOSPermissionDenied,
		Message:   CodeOSPermissionDenied.Detail(),
		retryable: true,
	}
}
