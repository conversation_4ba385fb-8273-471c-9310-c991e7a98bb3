package converter

import (
	"bufio"
	"io"
	"snapfile/pkg/progress"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type progressMonitor struct {
	scanner      *bufio.Scanner
	done         atomic.Int64
	total        int64
	_type        string
	doneOne      sync.Once
	readerOne    sync.Once
	stop         chan struct{}
	callback     progress.ReportCallback
	lastTimeDone int64
}

func newProgressMonitor(reader io.Reader, total int64, callback progress.ReportCallback) progress.Monitor {
	return &progressMonitor{
		scanner:  bufio.NewScanner(reader),
		stop:     make(chan struct{}),
		callback: callback,
		total:    total,
		_type:    "convert",
	}
}

func (g *progressMonitor) getFfmpegCurrentProgress(line string) (int64, bool) {
	if !strings.Contains(line, "out_time_us=") {
		return 0, false
	}
	res := strings.Split(line, "=")
	if len(res) != 2 {
		return 0, false
	}
	msStr := res[1]
	ms, err := strconv.ParseInt(strings.TrimSpace(msStr), 10, 64)
	if err != nil {
		return 0, false
	}
	return ms, true
}

func (g *progressMonitor) Start() {
	g.readerOne.Do(func() {
		go func() {
			for g.scanner.Scan() {
				line := g.scanner.Text()
				//slog.Debug("FFmpeg输出", "line", line)
				if ms, ok := g.getFfmpegCurrentProgress(line); ok {
					g.Report(ms)
				}
			}
		}()
	})
}

func (g *progressMonitor) Report(done int64) {
	g.doneOne.Do(func() {
		go func() {
			ticker := time.NewTicker(time.Second)
			defer ticker.Stop()
			for {
				select {
				case <-g.stop:
					ticker.Stop()
					//g.callback(g._type, g.total, g.done.Load()) // 最后一次上报
					return
				case <-ticker.C:
					currentDone := g.done.Load()
					speed := max(currentDone-g.lastTimeDone, 0)

					// 计算剩余时间
					var remainingTime int64 = 0
					if speed > 0 && currentDone < g.total {
						remainingTime = (g.total - currentDone) / speed
					}

					g.lastTimeDone = currentDone
					g.callback(g._type, g.total, g.done.Load(), speed, remainingTime)
				}
			}
		}()
	})
	g.done.Store(done)
}

func (g *progressMonitor) Stop() {
	close(g.stop)
}
