# Snapfile 项目文档

## 🏗️ 项目总览

**Snapfile** 是一个Go语言开发的文件下载和转换工具，主要用于下载网络文件（特别是视频文件）并进行格式转换。项目采用分阶段处理架构，支持断点续传、并发下载和媒体格式转换。

项目文档： https://gys-tech.feishu.cn/wiki/L2ruwLIsxivmpak4YRGcbRYAnZf

## 📁 目录结构

### **cmd/** - 命令行接口
```
cmd/
└── cmd.go - 命令行参数解析和应用启动逻辑
```
**功能**：处理命令行参数（如FFmpeg路径、最大下载任务数等），初始化日志系统并启动应用。

### **internal/** - 核心内部模块
项目的核心业务逻辑，按模块划分：

#### **internal/config/** - 配置管理
```
config/
└── config.go - 全局配置定义（FFmpeg路径、下载任务数等）
```

#### **internal/constant/** - 常量定义
```
constant/
└── constant.go - 项目常量（输出格式等）
```

#### **internal/entity/** - 数据模型
```
entity/
└── task.go - 任务实体定义（文件、分片、任务状态等）
```

#### **internal/event/** - 事件系统
```
event/
├── code.go - 事件代码定义
├── error.go - 错误处理
└── event.go - 事件结构体和处理函数
```

#### **internal/handler/** - 事件处理器
```
handler/
├── say_hello.go - 示例处理器
├── start_task.go - 任务启动处理器（核心业务逻辑）
...
```

#### **internal/schema/** - 数据模式
```
schema/
├── downloader.go - 下载器相关数据结构
├── schema.go - 通用数据结构
├── start_task.go - 启动任务请求/响应结构
...
```

#### **internal/server/** - 服务器实现
```
server/
└── stdio_server.go - 标准输入输出服务器，注册事件处理器
```

#### **internal/stage/** - 分阶段处理核心 🎯
```
stage/
├── runner.go - 阶段执行器，定义处理流水线
├── converter/ - 转换阶段
│   ├── cmd.go - FFmpeg命令构建
│   ├── converter.go - 转换器主逻辑
│   └── progress_monitor.go - 转换进度监控
├── downloader/ - 下载阶段
│   ├── download.go - 下载核心逻辑（支持分片、断点续传）
│   └── progress_monitor.go - 下载进度监控
├── move/ - 文件移动阶段
│   └── move.go - 文件最终移动到目标目录
└── prepare/ - 准备阶段
    ├── chunk.go - 分片处理
    ├── parse.go - URL解析和文件信息获取
    └── prepare.go - 准备阶段主逻辑
```

#### **internal/task/** - 任务管理
```
task/
└── manager.go - 任务上下文管理（运行中任务的生命周期）
```

#### **pkg/ffmpeg/** - FFmpeg工具包
```
ffmpeg/
├── ffmpeg.go - FFmpeg命令执行包装器
└── ffprobe.go - FFprobe媒体信息探测工具
```

#### **pkg/hash/** - 哈希工具
```
hash/
└── md5.go - MD5哈希计算工具
```

#### **pkg/progress/** - 进度跟踪
```
progress/
└── progress.go - 进度计算和监控工具
```

#### **pkg/shard/** - 分片处理
```
shard/
└── shard.go - 文件分片逻辑
```

## 🔄 核心工作流程

1. **准备阶段** (`prepare/`) - 解析URL，获取文件信息，计算分片
2. **下载阶段** (`downloader/`) - 并发下载文件分片，支持断点续传
3. **转换阶段** (`converter/`) - 使用FFmpeg进行格式转换和文件合并
4. **移动阶段** (`move/`) - 将完成的文件移动到最终目录

## 🎯 项目特点

- **分阶段处理**：清晰的处理流水线，支持断点续传
- **并发下载**：支持多任务并发和分片下载
- **格式转换**：集成FFmpeg进行视频/音频格式转换
- **进度监控**：实时进度跟踪和状态反馈
- **事件驱动**：基于事件的异步处理架构

---