package store

import (
	"encoding/json"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"sync"
)

var mutex sync.RWMutex

func Get(storeDir, taskID string) (*entity.Task, error) {
	mutex.RLock()
	defer mutex.RUnlock()
	f, err := os.OpenFile(filepath.Join(storeDir, "task.json"), os.O_RDONLY, 0644)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, nil
		}
		slog.Error("打开任务文件失败", "error", err)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	defer f.Close()
	b, err := io.ReadAll(f)
	if err != nil {
		slog.Error("读取任务文件内容失败", "error", err)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	var task entity.Task
	if err = json.Unmarshal(b, &task); err != nil {
		slog.Error("解析任务JSON数据失败", "error", err)
		return nil, event.ErrPrepareError().Wrap(err)
	}
	return &task, nil
}

func Save(storeDir string, data entity.Task) error {
	mutex.Lock()
	defer mutex.Unlock()
	if err := os.MkdirAll(storeDir, os.ModePerm); err != nil {
		slog.Error("创建任务存储目录失败", "error", err)
		return event.ErrPrepareError().Wrap(err)
	}
	f, err := os.OpenFile(filepath.Join(storeDir, "task.json"), os.O_CREATE|os.O_WRONLY, os.ModePerm)
	if err != nil {
		slog.Error("创建任务文件失败", "error", err)
		return event.ErrPrepareError().Wrap(err)
	}
	defer f.Close()
	decoder := json.NewEncoder(f)
	decoder.SetIndent("", "  ")
	if err := decoder.Encode(data); err != nil {
		slog.Error("编码任务JSON数据失败", "error", err)
		return event.ErrPrepareError().Wrap(err)
	}
	return nil
}
