package ffmpeg

import (
	"bufio"
	"context"
	"io"
	"os/exec"
	"snapfile/internal/config"
)

type media struct {
	filepath string
	_type    string
}

type CmdBuilder struct {
	inputFilePaths []media
	outputFilePath string
	globalArgs     []string
	args           []string
	outputCallback func(line string)
}

func NewBuilder() *CmdBuilder {
	return &CmdBuilder{}
}

func (b *CmdBuilder) InputFilePath(inputFilePath string) *CmdBuilder {
	b.inputFilePaths = append(b.inputFilePaths, media{
		filepath: inputFilePath,
	})
	return b
}

func (b *CmdBuilder) InputVideo(inputFilePath string) *CmdBuilder {
	b.inputFilePaths = append(b.inputFilePaths, media{
		filepath: inputFilePath,
		_type:    "video",
	})
	return b
}

func (b *CmdBuilder) InputAudio(inputFilePath string) *CmdBuilder {
	b.inputFilePaths = append(b.inputFilePaths, media{
		filepath: inputFilePath,
		_type:    "audio",
	})
	return b
}

func (b *CmdBuilder) InputSubtitle(inputFilePath string) *CmdBuilder {
	b.inputFilePaths = append(b.inputFilePaths, media{
		filepath: inputFilePath,
		_type:    "subtitle",
	})
	return b
}

func (b *CmdBuilder) OutputFilePath(outputFilePath string) *CmdBuilder {
	b.outputFilePath = outputFilePath
	return b
}

func (b *CmdBuilder) Progress() *CmdBuilder {
	b.args = append(b.args, "-progress", "pipe:1")
	return b
}

func (b *CmdBuilder) AddArgs(args ...string) *CmdBuilder {
	b.args = append(b.args, args...)
	return b
}

func (b *CmdBuilder) GetArgs() []string {
	return b.build()
}

func (b *CmdBuilder) AddGlobalArgs(args ...string) *CmdBuilder {
	b.globalArgs = append(b.globalArgs, args...)
	return b
}

func (b *CmdBuilder) Quiet() *CmdBuilder {
	return b.AddGlobalArgs("-hide_banner", "-loglevel", "error")
}

func (b *CmdBuilder) build() []string {
	cmd := make([]string, 0)
	cmd = append(cmd, b.globalArgs...) // global args first
	for _, path := range b.inputFilePaths {
		cmd = append(cmd, "-i", path.filepath)
	}
	cmd = append(cmd, b.args...)
	cmd = append(cmd, "-y") // auto overwrite
	if b.outputFilePath != "" {
		cmd = append(cmd, b.outputFilePath)
	}
	return cmd
}

func (b *CmdBuilder) OnOutput(fn func(line string)) *CmdBuilder {
	b.outputCallback = fn
	return b
}

func (b *CmdBuilder) cmd(ctx context.Context) (*exec.Cmd, error) {
	args := b.build()
	cmd := exec.CommandContext(ctx, config.GlobalConfig.FFmpegPath, args...)
	if b.outputCallback != nil {
		stdoutPipe, err := cmd.StdoutPipe()
		if err != nil {
			return nil, err
		}
		stderrPipe, err := cmd.StderrPipe()
		if err != nil {
			return nil, err
		}
		scanner := bufio.NewScanner(io.MultiReader(stdoutPipe, stderrPipe))
		go func() {
			for scanner.Scan() {
				line := scanner.Text()
				b.outputCallback(line)
			}
		}()
	}
	return cmd, nil
}

// Run 同步执行
func (b *CmdBuilder) Run(ctx context.Context) error {
	cmd, err := b.cmd(ctx)
	if err != nil {
		return err
	}
	return cmd.Run()
}
