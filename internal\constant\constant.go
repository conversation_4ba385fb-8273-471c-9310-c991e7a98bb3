package constant

const (
	ChunkPartExtension = ".part" // 分片文件的扩展名
	M3u8Extension      = ".m3u8" // m3u8文件后缀名
	TempFileExtension  = ".tmp"  // 临时文后缀
)

// 各个阶段输出的目录
const (
	DownloadOutputDir   = "download"   // 下载目录
	ConvertedOutputDir  = "converted"  // 已转换文件的输出目录
	ConvertingOutputDir = "converting" // 转换中文件的输出目录
)

// MaxDownloadConcurrent 每个任务下载最大协程
const MaxDownloadConcurrent = 8

// CtxLiveCancelKeyPrefix context中直播取消信号的key前缀
const CtxLiveCancelKeyPrefix = "live_cancel_"

// CtxProxyKey context中代理的key
const CtxProxyKey = "proxy"

// CtxDownloadSemKey context中下载信号的key
const CtxDownloadSemKey = "download_sem"

// CtxConvertSemKey context中转换信号的key
const CtxConvertSemKey = "convert_sem"

// LogTaskIDKey 日志中任务ID的key
const LogTaskIDKey = "taskID"

// MasterFileKey 表示第一个文件/主文件的表示符
const MasterFileKey = "first"

const (
	FileTypeOther    = "other"
	FileTypeVideo    = "video"
	FileTypeAudio    = "audio"
	FileTypeSubtitle = "subtitle"
	FileTypeImage    = "image"
)

// BlackListImageToJPG 需要转换为jpg的图片格式
var BlackListImageToJPG = []string{"heic", "heics", "hif", "heif", "avif", "webp"}

// TaskFileName 任务文件名
const TaskFileName = "task.json"

// NetErrRetryMaxCount 网络错误重试最大次数
const NetErrRetryMaxCount = 2
