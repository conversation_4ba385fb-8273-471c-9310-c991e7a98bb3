# 代码风格和约定

## 命名约定
- **包名**: 小写，简短，描述性（如 `config`, `entity`, `handler`）
- **结构体**: PascalCase（如 `Config`, `Task`, `File`）
- **函数/方法**: PascalCase（公开）或 camelCase（私有）
- **常量**: PascalCase 或 UPPER_CASE（如 `CodeSuccess`, `MAX_DOWNLOAD_CONCURRENT`）
- **变量**: camelCase

## 项目结构约定
- `internal/` - 内部包，不对外暴露
- `pkg/` - 可复用的工具包
- `cmd/` - 命令行相关代码
- 按功能模块组织代码（如 `handler/`, `stage/`, `entity/`）

## 错误处理
- 使用自定义 `Error` 结构体，包含错误代码和消息
- 支持错误包装和重试标记
- 错误代码使用常量定义（如 `CodeSuccess`, `CodeDownloadError`）

## 日志记录
- 使用标准库 `log/slog` 进行结构化日志
- 支持不同日志级别：debug, info, warn, error
- 在 debug 模式下包含源码位置信息

## 注释风格
- 结构体和公开函数使用中文注释
- 注释简洁明了，描述功能和用途
- 重要的业务逻辑添加详细注释

## 测试约定
- 测试文件命名：`*_test.go`
- 使用标准库 `testing` 包
- 测试函数命名：`TestXxx`
- 使用表驱动测试模式