package converter

import (
	"bufio"
	"context"
	"io"
	"log/slog"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"slices"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/pkg/hash"
	"snapfile/pkg/progress"
	"snapfile/pkg/semaphore"
	"snapfile/pkg/tools"
	"snapfile/pkg/transport/stdio"
	"strings"

	"github.com/gabriel-vasile/mimetype"
	"github.com/sunshineplan/imgconv"
)

func getConvertedOutputFilePath(task *entity.Task) string {
	return filepath.Join(task.TempDir, constant.ConvertedOutputDir)
}

// 获取文件流中音视频文件的总时长
func getTotalDuration(streams []stream, _type string) int64 {
	var total int64
	for _, s := range streams {
		if _type == constant.FileTypeVideo {
			if (s.Type == constant.FileTypeVideo || s.Type == constant.FileTypeAudio) && total < s.Duration {
				total = s.Duration
			}
		} else if _type == constant.FileTypeAudio {
			if s.Type == constant.FileTypeAudio && total < s.Duration {
				total = s.Duration
			}
		}
	}
	return total
}

// Handler 转换视频文件
func Handler(ctx context.Context, task *entity.Task) (err error) {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	// 从上下文获取转换信号量
	sem := tools.GetValue(ctx, constant.CtxConvertSemKey).(*semaphore.Semaphore)
	if err = sem.Acquire(ctx); err != nil {
		return err
	}
	defer sem.Release()
	// 发送转换开始事件
	stdio.Post(event.NewOutput(event.CodeTaskStartConversion, event.H{
		"taskID": task.ID,
	}))
	// 转换失败，将转换中的文件夹删除
	defer func() {
		if err != nil {
			outputDir := filepath.Join(task.TempDir, constant.ConvertingOutputDir)
			if err := os.RemoveAll(outputDir); err != nil {
				slog.WarnContext(ctx, "删除转换临时文件失败", "err", err, "dir", outputDir)
			}
		}
	}()
	// 如果已经转换完毕，则直接返回
	// 判断是否已经转换完毕
	convertOutputFilePath := getConvertedOutputFilePath(task)
	if _, err := os.Stat(convertOutputFilePath); err == nil {
		return nil
	}
	// 创建一个子上下文，用于取消转换
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	// 从下载目录获取下载完成的文件，并得到文件的媒体流信息
	fileStreams := make([]stream, 0)
	dirs, err := os.ReadDir(filepath.Join(task.TempDir, constant.DownloadOutputDir))
	if err != nil {
		slog.WarnContext(ctx, "读取下载目录失败", "err", err)
		return event.ErrConvertError().Wrap(err)
	}
	for _, f := range dirs {
		if f.IsDir() {
			continue
		}
		if strings.HasPrefix(f.Name(), ".") {
			continue
		}
		streams, err := GetFileStreams(ctx, filepath.Join(task.TempDir, constant.DownloadOutputDir, f.Name()))
		if err != nil {
			return err
		}
		fileStreams = append(fileStreams, streams...)
	}
	slices.SortStableFunc(fileStreams, func(a, b stream) int { // 将主文件排到第一位
		if strings.Contains(path.Base(a.Path), constant.MasterFileKey) {
			return -2
		}
		return strings.Compare(a.Language, b.Language) // 其他的按语言排序
	})
	// 确保输出目录存在
	outputDir := filepath.Join(task.TempDir, constant.ConvertingOutputDir)
	if err := os.MkdirAll(outputDir, os.ModePerm); err != nil {
		slog.WarnContext(ctx, "创建转换输出目录失败", "err", err, "outputDir", outputDir)
		return event.ErrConvertError().Wrap(err)
	}
	// 构建转换命令
	var cmd *exec.Cmd
	var others []stream
	var ok bool
	var total int64
	if task.OutputType == constant.FileTypeVideo { // 如果用户想要转换视频,但是文件流信息中没有视频，只能转换音频
		cmd, others, ok = buildOutputVideoCmd(ctx, fileStreams, filepath.Join(task.TempDir, constant.ConvertingOutputDir, hash.MD5([]byte(task.Name))), task.OutputVideoFormat, task.EmbeddedSubtitle)
		if ok {
			total = getTotalDuration(fileStreams, constant.FileTypeVideo)
		}
	}
	if task.OutputType == constant.FileTypeAudio || !ok {
		cmd, others, ok = buildOutputAudioCmd(ctx, fileStreams, filepath.Join(task.TempDir, constant.ConvertingOutputDir, hash.MD5([]byte(task.Name))), task.OutputAudioFormat)
		if ok {
			total = getTotalDuration(fileStreams, constant.FileTypeAudio)
		}
	}
	if !ok {
		cmd, others, ok = buildOutputSubtitleCmd(ctx, fileStreams, filepath.Join(task.TempDir, constant.ConvertingOutputDir, hash.MD5([]byte(task.Name))))
	}
	// 如果存在需要转换的文件，则执行转换
	if ok {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		slog.DebugContext(ctx, "开始转换", "cmd", cmd.String())
		// 捕获ffmpeg输出
		stdoutPipe, err := cmd.StdoutPipe()
		if err != nil {
			return event.ErrConvertError().Wrap(err)
		}
		stderrPipe, err := cmd.StderrPipe()
		if err != nil {
			return event.ErrConvertError().Wrap(err)
		}
		cmdExecErr := make(chan error) // 命令执行错误打印日志标记
		diskFullErrFlag := false       // 磁盘空间不足标记
		go func() {                    // 构建一个协程，用于打印ffmpeg的错误日志
			sb := strings.Builder{}
			defer func() {
				select {
				case <-ctx.Done():
				case <-cmdExecErr:
					slog.WarnContext(ctx, "ffmpeg错误日志", "err", sb.String())
				}
			}()
			scanner := bufio.NewScanner(stderrPipe)
			for scanner.Scan() {
				line := scanner.Text()
				if strings.Contains(line, "Error") { // ffmepg运行期间检测到错误
					if strings.Contains(line, "No space left on device") { // 磁盘空间不足错误
						diskFullErrFlag = true
					}
					sb.WriteString(line)
					sb.WriteRune('\n')
				}
			}
		}()
		// 进度监听
		var reporter progress.Monitor
		if task.ConvertProgressCallback != nil {
			reporter = newProgressMonitor(stdoutPipe, total, task.ConvertProgressCallback)
			reporter.Start()
			defer reporter.Stop()
		}
		// 启动命令
		if err := cmd.Start(); err != nil {
			slog.WarnContext(ctx, "转换命令启动失败", "err", err, "cmd", cmd.String())
			close(cmdExecErr)
			return event.ErrConvertError().Wrap(err)
		}
		// 等待命令完成
		if err := cmd.Wait(); err != nil {
			slog.WarnContext(ctx, "转换命令执行失败", "err", err, "cmd", cmd.String())
			close(cmdExecErr)
			if diskFullErrFlag {
				return event.ErrDiskFullError().Wrap(err)
			}
			return event.ErrConvertError().Wrap(err)
		}
	}
	// 将其他文件复制一份
	for _, otherStream := range others {
		// 构建输出文件路径
		outputFilePath := filepath.Join(outputDir, filepath.Base(otherStream.Path))
		outputFilePath = strings.TrimSuffix(outputFilePath, path.Ext(outputFilePath)) // 移除原后缀
		var ext string
		mtype, _ := mimetype.DetectFile(otherStream.Path) // 获取mime类型
		if mtype != nil && mtype.Extension() != "" {
			ext = mtype.Extension()
		}
		if slices.Contains(constant.BlackListImageToJPG, otherStream.CodecName) { // 如果文件是图片，则转换为jpg
			ext = ".jpg"
		}
		outputFilePath = outputFilePath + ext
		err = copyFile(ctx, otherStream, outputFilePath)
		if err != nil {
			return err
		}
	}
	// 重命名转换中目录为转换输出目录
	err = os.Rename(outputDir, filepath.Join(task.TempDir, constant.ConvertedOutputDir))
	if err != nil {
		slog.WarnContext(ctx, "重命名转换输出目录失败", "err", err)
		return event.ErrConvertError().Wrap(err)
	}
	return nil
}

// 复制文件, 如果文件是图片，则转换为jpg
func copyFile(ctx context.Context, otherStream stream, outputFilePath string) (err error) {
	if otherStream.Type == constant.FileTypeImage {
		if slices.Contains(constant.BlackListImageToJPG, otherStream.CodecName) {
			err = toJPEG(otherStream.Path, outputFilePath)
			if err == nil {
				return nil
			}
			// 失败了，继续走正常移动流程
			slog.WarnContext(ctx, "转换图片为jpg失败", "err", err, "srcFilePath", otherStream.Path)
		}
		if slices.Contains(constant.BlackListImageToJPG, path.Ext(otherStream.Path)) {
			err = toJPEG(otherStream.Path, outputFilePath)
			if err == nil {
				return nil
			}
			// 失败了，继续走正常移动流程
			slog.WarnContext(ctx, "转换图片为jpg失败", "err", err, "srcFilePath", otherStream.Path)
		}
		// 常见格式就直接移动
	}
	fw, err := os.OpenFile(outputFilePath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	if err != nil {
		slog.WarnContext(ctx, "创建转换输出文件失败", "err", err)
		return event.ErrConvertError().Wrap(err)
	}
	defer fw.Close()
	fr, err := os.OpenFile(otherStream.Path, os.O_RDONLY, os.ModePerm)
	if err != nil {
		slog.WarnContext(ctx, "打开转换输入文件失败", "err", err)
		return event.ErrConvertError().Wrap(err)
	}
	defer fr.Close()
	_, err = io.Copy(fw, fr)
	if err != nil {
		slog.WarnContext(ctx, "复制转换输入文件失败", "err", err)
		return event.ErrConvertError().Wrap(err)
	}
	return nil
}

// 将图片转换为jpg
func toJPEG(srcPath string, dstPath string) error {
	src, err := imgconv.Open(srcPath)
	if err != nil {
		return err
	}
	w, err := os.OpenFile(dstPath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	if err != nil {
		return err
	}
	defer w.Close()
	err = imgconv.Write(w, src, &imgconv.FormatOption{Format: imgconv.JPEG})
	if err != nil {
		return err
	}
	return nil
}
