package shard

import "testing"

func TestChunk(t *testing.T) {
	t.Log(AutoChunkSize(int64(100*MB)) / MB)
}

func TestSplitBySize(t *testing.T) {
	chunks, err := SplitBySize(int64(1*MB), 5*MB)
	if err != nil {
		t.<PERSON>rror(err)
		return
	}
	for _, chunk := range chunks {
		t.Logf("index: %d, start: %s, lenght: %s\n", chunk.Index, UnitFormat(ByteSize(chunk.Offset)), UnitFormat(ByteSize(chunk.Length)))
	}
}
