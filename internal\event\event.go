package event

import (
	"encoding/json"
	"errors"
)

type H map[string]any

// Input 事件基础结构
type Input struct {
	Type    string `json:"type"`
	Payload any    `json:"payload,omitempty"`
}

func GetPayload[T any](i *Input) T {
	var data T
	jsonData, _ := json.Marshal(i.Payload)
	_ = json.Unmarshal(jsonData, &data)
	return data
}

type Output struct {
	Code      Code   `json:"code"`
	Data      any    `json:"data,omitempty"`
	Message   string `json:"message,omitempty"`
	Retryable bool   `json:"retryable,omitempty"`
}

func NewOutput(code Code, data any) *Output {
	return &Output{
		Code:    code,
		Data:    data,
		Message: code.Detail(),
	}
}

type outputOption struct {
	taskID string
}

type OutputOption func(o *outputOption)

func WithTaskID(taskID string) OutputOption {
	return func(o *outputOption) {
		o.taskID = taskID
	}
}

func NewErrOutput(err error, opts ...OutputOption) *Output {
	var e *Error
	o := &outputOption{}
	for _, opt := range opts {
		opt(o)
	}
	h := H{}
	if o.taskID != "" {
		h["taskID"] = o.taskID
	}
	if errors.As(err, &e) {
		return &Output{
			Code:      e.Code,
			Message:   e.Message,
			Retryable: e.retryable,
			Data:      h,
		}
	}
	return &Output{
		Code:    CodeUnknownError,
		Message: err.Error(),
		Data:    h,
	}
}

func TaskComplete(taskID string, files []string) *Output {
	return NewOutput(CodeTaskComplete, H{
		"taskID": taskID,
		"files":  files,
	})
}

func TaskLiveDetected(taskID string) *Output {
	return NewOutput(CodeTaskLiveDetected, H{
		"taskID": taskID,
	})
}

func TaskDeleted(taskID string) *Output {
	return NewOutput(CodeTaskDeleted, H{
		"taskID": taskID,
	})
}

func TaskDownloadProgress(taskID string, total, done, speed, remainingTime int64) *Output {
	return NewOutput(CodeTaskDownloadProgress, H{
		"taskID":        taskID,
		"total":         total,
		"done":          done,
		"speed":         speed,
		"remainingTime": remainingTime,
	})
}

func TaskConvertProgress(taskID string, total, done, speed, remainingTime int64) *Output {
	return NewOutput(CodeTaskConversionProgress, H{
		"taskID":        taskID,
		"total":         total,
		"done":          done,
		"speed":         speed,
		"remainingTime": remainingTime,
	})
}

func StopRecordingLive(taskID string) *Output {
	return NewOutput(CodeStopRecordingLive, H{
		"taskID": taskID,
	})
}
