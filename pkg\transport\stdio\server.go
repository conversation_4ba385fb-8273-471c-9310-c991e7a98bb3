package stdio

import (
	"bufio"
	"context"
	"encoding/json"
	"os"
	"snapfile/internal/event"
	"sync"
)

type HandlerFunc func(ctx context.Context, msg *event.Input) (*event.Output, error)

type Server struct {
	reader   *bufio.Scanner
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
	started  bool
	mu       sync.RWMutex
	handlers map[string]HandlerFunc
}

// NewServer 创建一个新的stdio server
func NewServer() *Server {
	return &Server{
		reader:   bufio.NewScanner(os.Stdin),
		handlers: make(map[string]HandlerFunc),
	}
}

// Start 启动server，开始监听stdin输入
func (s *Server) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	initNotify()
	startNotify()

	if s.started {
		return nil
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.started = true

	// 启动消息读取goroutine
	s.wg.Add(1)
	go s.readMessages()

	return nil
}

// Stop 停止server
func (s *Server) Stop(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.started {
		return nil
	}

	s.started = false
	if s.cancel != nil {
		s.cancel()
	}

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// readMessages 读取stdin消息的内部方法
func (s *Server) readMessages() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			if !s.reader.Scan() {
				return
			}
			line := s.reader.Text()
			var msg event.Input
			err := json.Unmarshal([]byte(line), &msg)
			if err != nil {
				continue
			}
			handler, ok := s.handlers[msg.Type]
			if !ok {
				Post(event.NewErrOutput(event.ErrUnknownEvent()))
				continue
			}
			e, err := handler(s.ctx, &msg)
			if err != nil {
				Post(event.NewErrOutput(err))
				continue
			}
			if e != nil {
				Post(e)
			}
		}
	}
}

// IsStarted 检查server是否已启动
func (s *Server) IsStarted() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.started
}

func (s *Server) Register(eventType string, handler HandlerFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.handlers[eventType] = handler
}
