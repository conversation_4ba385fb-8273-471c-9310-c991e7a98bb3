package downloader

import (
	"context"
	"encoding/json"
	"log/slog"
	"os"
	"snapfile/internal/entity"
	"testing"
	"time"
)

func TestHandler(t *testing.T) {
	loggerHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		AddSource: true,
	})
	logger := slog.New(loggerHandler)
	slog.SetDefault(logger)
	data, err := os.ReadFile(".snapfile/123dd/task.json")
	if err != nil {
		t.Error(err)
	}
	var task entity.Task
	err = json.Unmarshal(data, &task)
	if err != nil {
		t.Error(err)
	}
	err = <PERSON><PERSON>(context.Background(), &task)
	if err != nil {
		t.Error(err)
	}
}

func toJson(a any) string {
	data, _ := json.MarshalIndent(a, "", "  ")
	return string(data)
}

func TestLive(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	next := GetChunk(ctx, true, entity.File{
		URL: "http://liveout.btzx.com.cn/62ds9e/yil08g.m3u8",
	})
	go func() {
		time.Sleep(time.Second * 15)
		cancel()
	}()
	for i, chunk := range next {
		t.Logf("%d: %s", i, toJson(chunk))
	}
}
