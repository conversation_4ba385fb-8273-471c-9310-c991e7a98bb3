package app

import (
	"context"
	"os"
	"snapfile/pkg/transport"
)

type option struct {
	ctx     context.Context
	sigs    []os.Signal
	servers []transport.Server
}

type Options func(*option)

func WithContext(ctx context.Context) Options {
	return func(o *option) {
		o.ctx = ctx
	}
}

func WithSigs(sigs ...os.Signal) Options {
	return func(o *option) {
		o.sigs = sigs
	}
}

func WithServers(servers ...transport.Server) Options {
	return func(o *option) {
		o.servers = servers
	}
}
