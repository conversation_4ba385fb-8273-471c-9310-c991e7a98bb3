package handler

import (
	"context"
	"snapfile/internal/event"
	"snapfile/internal/schema"
	"snapfile/internal/task"
	"snapfile/pkg/tools"
)

func StopRecordingLiveHandler(_ context.Context, e *event.Input) (*event.Output, error) {
	req := event.GetPayload[schema.StopRecordingLiveReq](e)
	if err := tools.Validate(req); err != nil {
		return nil, err
	}
	task.StopRecordingLive(req.TaskID)
	return nil, nil
}
