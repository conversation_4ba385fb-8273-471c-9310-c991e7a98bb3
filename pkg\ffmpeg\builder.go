package ffmpeg

import (
	"context"
	"fmt"
	"slices"
	"strings"
)

type MapFunc func(stream Stream, index int) (outputFile string, ok bool)

type streamMap struct {
	stream     []Stream
	outputFile string
}

type Builder struct {
	inputs        []string
	streams       []Stream
	streamMapFn   MapFunc
	outputFileMap []streamMap
}

func New() *Builder {
	return &Builder{}
}

func (r *Builder) AddInput(filePaths ...string) *Builder {
	r.inputs = append(r.inputs, filePaths...)
	return r
}

func (r *Builder) Map(fn MapFunc) *Builder {
	r.streamMapFn = fn
	return r
}

func (r *Builder) Run(ctx context.Context) error {
	for _, input := range r.inputs {
		res, err := GetFFProbeRes(ctx, input)
		if err != nil {
			return err
		}
		r.streams = append(r.streams, res.Streams...)
	}
	for i, stream := range r.streams {
		outputFile, ok := r.streamMapFn(stream, i)
		if !ok {
			continue
		}
		index := slices.IndexFunc(r.outputFileMap, func(outputMap streamMap) bool {
			return outputMap.outputFile == outputFile
		})
		if index == -1 {
			r.outputFileMap = append(r.outputFileMap, streamMap{
				stream:     []Stream{stream},
				outputFile: outputFile,
			})
		} else {
			r.outputFileMap[index].stream = append(r.outputFileMap[index].stream, stream)
		}
	}
	args := make([]string, 0)
	for _, item := range r.outputFileMap {
		for _, stream := range item.stream { // 添加输入
			args = append(args, "-i", stream.Format.Filename)
		}
	}
	i := 0
	subtitleIndex := 0
	for _, item := range r.outputFileMap {
		for _, stream := range item.stream { // 映射
			switch stream.CodecType {
			case "video":
				args = append(args, "-map", buildMapArgs(i, stream.CodecType))
			case "audio":
				args = append(args, "-map", buildMapArgs(i, stream.CodecType))
			case "subtitle":
				args = append(args, "-map", buildMapArgs(i, stream.CodecType))
				args = append(args, fmt.Sprintf("-metadata:s:s:%d", subtitleIndex), "title", "Chinese")
				subtitleIndex++
			default:
			}
			i++
		}
		args = append(args, "-c:v", "copy", "-c:a", "copy", item.outputFile)
	}
	fmt.Println("ffmpeg", strings.Join(args, " "))
	return nil
}

func buildMapArgs(index int, _type string) string {
	if _type == "video" {
		return fmt.Sprintf("%d:v:0", index)
	} else if _type == "audio" {
		return fmt.Sprintf("%d:a:0", index)
	} else if _type == "subtitle" {
		return fmt.Sprintf("%d:s:0", index)
	} else {
		return ""
	}
}
