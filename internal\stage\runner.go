package stage

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/internal/stage/converter"
	"snapfile/internal/stage/downloader"
	"snapfile/internal/stage/move"
	"snapfile/internal/stage/prepare"
	"snapfile/pkg/transport/stdio"
)

type TaskStage = string

const (
	TaskStagePrepare  TaskStage = "prepare"
	TaskStageDownload TaskStage = "download"
	TaskStageConvert  TaskStage = "converter"
	TaskStageMove     TaskStage = "move"
)

func init() {
	RegisterStage(TaskStagePrepare, prepare.Handler, event.CodeTaskStartPrepare, event.CodeTaskPrepared)
	RegisterStage(TaskStageDownload, downloader.Handler, event.CodeTaskPendingDownload, event.CodeTaskDownloaded)
	RegisterStage(TaskStageConvert, converter.Handler, event.CodeTaskPendingConversion, event.CodeTaskConverted)
	RegisterStage(TaskStageMove, move.Handler, event.CodeTaskStartMove, event.CodeTaskMoved)
}

type StageHandler func(context.Context, *entity.Task) error

type Stage struct {
	Stage      TaskStage
	Handler    StageHandler
	startEvent event.Code
	doneEvent  event.Code
}

var pipeline []Stage

func RegisterStage(stage TaskStage, handler StageHandler, startEvent, doneEvent event.Code) {
	pipeline = append(pipeline, Stage{
		Stage:      stage,
		Handler:    handler,
		startEvent: startEvent,
		doneEvent:  doneEvent,
	})
}

func RunStages(ctx context.Context, task *entity.Task) error {
	for _, stage := range pipeline {
		stdio.Post(event.NewOutput(stage.startEvent, event.H{
			"taskID": task.ID,
		}))
		for {
			err := stage.Handler(ctx, task)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					slog.Debug("取消任务，删除临时文件")
					err := os.RemoveAll(task.TempDir)
					if err != nil {
						slog.Warn("删除临时文件失败", "err", err)
					}
				}
				return err
			}
			break
		}
		stdio.Post(event.NewOutput(stage.doneEvent, event.H{
			"taskID": task.ID,
		}))
	}
	return nil
}
