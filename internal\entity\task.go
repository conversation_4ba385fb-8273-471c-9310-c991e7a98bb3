package entity

import (
	"snapfile/pkg/progress"
)

type Task struct {
	ID                       string                  `json:"id,omitempty"`
	Name                     string                  `json:"name,omitempty"`      // 任务名称
	OutputDir                string                  `json:"outputDir,omitempty"` // 最终输出目录
	TempDir                  string                  `json:"tempDir"`             // 临时工作目录
	Files                    []File                  `json:"files"`               // 下载任务
	OutputType               string                  `json:"outputType"`          // 最终输出类型
	OutputVideoFormat        OutputFormat            `json:"outputVideoFormat"`   // 默认视频输出格式
	OutputAudioFormat        OutputFormat            `json:"outputAudioFormat"`   // 默认音频输出格式
	EmbeddedSubtitle         bool                    `json:"embeddedSubtitle"`    // 是否嵌入字幕
	Live                     bool                    `json:"live"`                // 是否直播
	Proxy                    string                  `json:"proxy"`               // 代理信息
	FinalizedFilePaths       []string                `json:"finalizedFilePaths"`  // 本次任务最终的输出文件路径
	DownloadProgressCallback progress.ReportCallback `json:"-"`                   // 下载进度回调
	ConvertProgressCallback  progress.ReportCallback `json:"-"`                   // 转换进度回调
}

type File struct {
	URL              string            `json:"url"`         // 下载链接
	URLType          URLType           `json:"urlType"`     // 下载链接的类型，可为空
	Size             int64             `json:"size"`        // 文件总大小
	Language         string            `json:"language"`    // 字幕语言
	Headers          map[string]string `json:"headers"`     // 请求头
	OptionalDownload bool              `json:"ignoreError"` // 可忽略下载错误
	Chunks           []Chunk           `json:"chunks"`      // 分片信息
}

type Chunk struct {
	Index          int     `json:"index,omitempty"`          // 第几块，从 0 开始
	Offset         int64   `json:"offset,omitempty"`         // 在原文件中的起始字节
	Length         int64   `json:"length,omitempty"`         // 该块长度（字节）
	Duration       float32 `json:"duration,omitempty"`       // 时长
	DownloadURL    string  `json:"downloadURL,omitempty"`    // 下载地址
	OutputFilePath string  `json:"outputFilePath,omitempty"` // 文件路径
}
