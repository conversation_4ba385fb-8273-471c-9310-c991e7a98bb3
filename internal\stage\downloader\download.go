package downloader

import (
	"context"
	"errors"
	"fmt"
	"golang.org/x/sync/errgroup"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/internal/store"
	"snapfile/pkg/hash"
	"snapfile/pkg/httpx"
	"snapfile/pkg/progress"
	"snapfile/pkg/semaphore"
	"snapfile/pkg/tools"
	"snapfile/pkg/transport/stdio"
	"time"
)

// contextReader 包装原始reader以支持上下文取消
type contextReader struct {
	ctx    context.Context
	reader io.Reader
}

func newContextReader(ctx context.Context, reader io.Reader) *contextReader {
	return &contextReader{
		ctx:    ctx,
		reader: reader,
	}
}

func (cr *contextReader) Read(p []byte) (n int, err error) {
	select {
	case <-cr.ctx.Done():
		return 0, context.Cause(cr.ctx)
	default:
	}
	n, err = cr.reader.Read(p)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 是否真的是取消
			if errors.Is(context.Cause(cr.ctx), context.Canceled) { // 真的是取消
				return n, event.ErrDownloadError().Wrap(err)
			} else if errors.Is(context.Cause(cr.ctx), errorStop) {
				return n, event.ErrDownloadError().Wrap(errorStop)
			}
		}
		return n, event.ErrDownloadError().Wrap(err)
	}
	return n, nil
}

var errorStop = errors.New("stop download")

func Handler(ctx context.Context, task *entity.Task) (err error) {
	sem := tools.GetValue(ctx, constant.CtxDownloadSemKey).(*semaphore.Semaphore)
	if err = sem.Acquire(ctx); err != nil {
		return err
	}
	defer sem.Release()
	stdio.Post(event.NewOutput(event.CodeTaskStartDownload, event.H{
		"taskID": task.ID,
	}))
	// 直播检测
	downloadCtx, downloadCancel := context.WithCancelCause(ctx)
	//defer downloadCancel(context.Canceled)
	if task.Live {
		var cancel context.CancelFunc = func() {
			downloadCancel(errorStop)
			stdio.Post(event.StopRecordingLive(task.ID))
		}
		tools.SetValue(ctx, constant.CtxLiveCancelKeyPrefix+task.ID, cancel)
	}
	// 进度监听
	var monitor progress.Monitor
	if task.DownloadProgressCallback != nil {
		total := int64(0)
		for _, file := range task.Files {
			total += file.Size
		}
		monitor = newGlobalProgressMonitor(total, task.DownloadProgressCallback)
		defer monitor.Stop()
	}
	// 创建下载任务
	g, gctx := errgroup.WithContext(ctx)
	downloadSem := semaphore.NewSemaphore(constant.MaxDownloadConcurrent) // 每一个下载任务最多只能有8个协程在执行下载操作，不管里面有多少个file
	for fileIndex, it := range task.Files {
		file := it
		g.Go(func() error {
			if _, err := os.Stat(getMergedOutputFilePath(task, file)); err == nil {
				return nil
			}
			fwg, _ := errgroup.WithContext(gctx)
			for i, item := range GetChunk(downloadCtx, task.Live, file) {
				chunk := item
				if task.Live && file.URLType == entity.URLTypeM3u8 { // m3u8直播重新定义分片文件，因为m3u8直播在准备阶段不做分片处理
					chunkOutputFile := filepath.Join(task.TempDir, constant.DownloadOutputDir, hash.MD5([]byte(file.URL)), fmt.Sprintf("%d%s", i, constant.ChunkPartExtension))
					if i == 0 { // 第一次开始时，判断直播是否已经下载过了，下载过了直接结束下载流程
						if _, err := os.Stat(chunkOutputFile); err == nil {
							// 没有错误，说明该m3u8直播已经下载过了，进入合并流程
							break
						}
					}
					chunk.OutputFilePath = chunkOutputFile
					task.Files[fileIndex].Chunks = append(task.Files[fileIndex].Chunks, chunk) // 直播只会有一个file，即一个任务协程，不会出现并发写冲突
					file = task.Files[fileIndex]
					store.Save(task.TempDir, *task)
				}
				fwg.Go(func() error { // 下载单独的一个分片
					if err := downloadSem.Acquire(downloadCtx); err != nil { // 获取下载令牌
						if errors.Is(err, errorStop) {
							return nil
						}
						return err
					}
					defer downloadSem.Release()
					retryCount := 1
					for {
						if err := downloadChunkToFile(downloadCtx, file, chunk, monitor); err != nil {
							if errors.Is(err, errorStop) {
								return nil
							}
							var netErr net.Error
							if errors.As(err, &netErr) && retryCount < constant.NetErrRetryMaxCount {
								slog.WarnContext(ctx, fmt.Sprintf("网络错误，第%d片 第%d次重试", chunk.Index+1, retryCount), "err", err)
								retryCount++
								time.Sleep(1 * time.Second)
								continue
							}
							return err
						}
						break // 正常执行完成无错误，跳出
					}
					return nil
				})
			}
			if err := fwg.Wait(); err != nil { //  等待file分片下载完成
				stdio.Post(event.NewOutput(event.CodeFileDownloadError, event.H{
					"taskID": task.ID,
					"url":    file.URL,
				}))
				if file.OptionalDownload {
					slog.WarnContext(ctx, "文件下载失败", "ignoreErr", true, "err", err)
					return nil
				}
				if task.Live && (errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF)) {
					// 当直播断开时，继续合并文件
					// pass, 方便理解
				} else {
					return err
				}
			}
			// 合并分片
			if err := mergeTaskChunk(gctx, task, file); err != nil {
				if file.OptionalDownload {
					slog.WarnContext(ctx, "文件合并失败", "ignoreErr", true, "err", err)
					return nil
				}
				return err
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

type option struct {
	RangeStart *int64
	RangeEnd   *int64
	Header     http.Header
}

func downloadChunkToFile(ctx context.Context, it entity.File, chunk entity.Chunk, monitor progress.Monitor) error {
	f, err := os.Stat(chunk.OutputFilePath) // 判断是否需要断点续传
	if err != nil && !os.IsNotExist(err) {
		slog.WarnContext(ctx, "检查分片文件状态失败", "err", err, "filePath", chunk.OutputFilePath)
		return event.ErrDownloadError().Wrap(err)
	}
	if f != nil && f.Size() >= chunk.Length {
		monitor.Report(f.Size())
		return nil
	}
	o := option{
		Header: make(http.Header, len(it.Headers)),
	}
	for k, v := range it.Headers {
		o.Header.Set(k, v)
	}
	// 断点续传
	if f != nil && f.Size() > 0 {
		start := f.Size() + chunk.Offset
		o.RangeStart = &start
		monitor.Report(f.Size())
	} else {
		o.RangeStart = &chunk.Offset
	}
	if chunk.Length > 0 {
		// HTTP Range 是包含边界的(inclusive)，所以结束位置要减1(左必右开)
		end := chunk.Offset + chunk.Length - 1
		o.RangeEnd = &end
	}
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		reader, err := getHTTPResponseBody(ctx, chunk.DownloadURL, o)
		if err != nil {
			return err
		}
		defer reader.Close()
		if monitor != nil {
			reader = newProgressReader(reader, monitor.Report)
		}
		err = writeFile(ctx, chunk.OutputFilePath, reader)
		if err != nil {
			return err
		}
	}
	return nil
}

func getHTTPResponseBody(ctx context.Context, fileURL string, o option) (io.ReadCloser, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fileURL, nil)
	if err != nil {
		slog.WarnContext(ctx, "创建下载HTTP请求失败", "err", err, "url", fileURL)
		return nil, event.ErrDownloadError().Wrap(err)
	}

	// 设置自定义头部
	if o.Header != nil {
		req.Header = o.Header
	}

	// 设置Range头部
	if o.RangeStart != nil && o.RangeEnd != nil {
		req.Header.Set("Range", fmt.Sprintf("bytes=%d-%d", *o.RangeStart, *o.RangeEnd))
	}

	// 使用标准http客户端进行流式下载
	resp, err := httpx.GetClient(tools.GetProxyFromContext(ctx)).Do(req)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return nil, err
		}
		slog.WarnContext(ctx, "执行下载HTTP请求失败", "err", err, "url", fileURL)
		return nil, event.ErrDownloadError().Wrap(err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusPartialContent {
		_ = resp.Body.Close()
		slog.WarnContext(ctx, "下载HTTP响应状态码错误", "status", resp.Status, "statusCode", resp.StatusCode, "url", fileURL)
		return nil, event.ErrDownloadError().Wrap(fmt.Errorf("http code: %d", resp.StatusCode))
	}

	return resp.Body, nil
}

func writeFile(ctx context.Context, outputFilePath string, reader io.Reader) error {
	dir := filepath.Dir(outputFilePath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		slog.WarnContext(ctx, "创建下载目录失败", "err", err, "dir", dir)
		return event.ErrDownloadError().Wrap(err)
	}
	f, err := os.OpenFile(outputFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, os.ModePerm)
	if err != nil {
		slog.WarnContext(ctx, "创建下载文件失败", "err", err, "filePath", outputFilePath)
		return event.ErrDownloadError().Wrap(err)
	}
	defer f.Close()

	_, err = io.Copy(f, newContextReader(ctx, reader))
	if err != nil && !errors.Is(err, io.EOF) && !errors.Is(err, errorStop) {
		slog.WarnContext(ctx, "下载文件拷贝失败", "err", err, "filePath", outputFilePath)
		return event.ErrDownloadError().Wrap(err)
	}

	return nil
}

// 获取合并后的文件路径
func getMergedOutputFilePath(task *entity.Task, file entity.File) string {
	u, _ := url.Parse(file.URL)
	ext := filepath.Ext(u.Path)
	if ext == "" {
		ext = constant.TempFileExtension
	}
	if len(task.Files) > 0 && task.Files[0].URL == file.URL {
		ext = fmt.Sprintf("_%s%s", constant.MasterFileKey, ext)
	}
	filename := hash.MD5([]byte(file.URL)) + ext
	if file.Language != "" {
		filename = tools.SetLanguage(filename, file.Language)
	}
	return path.Join(task.TempDir, constant.DownloadOutputDir, filename)
}

func mergeTaskChunk(ctx context.Context, task *entity.Task, file entity.File) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	if _, err := os.Stat(getMergedOutputFilePath(task, file)); err == nil { // 文件存在说明已下载，跳过
		return nil
	}
	err := mergeFileChunk(ctx, getMergedOutputFilePath(task, file), file)
	if err != nil {
		return err
	}
	return nil
}

// 默认方式合并块: 按下载的字节直接合并
func mergeFileChunk(ctx context.Context, mergedOutputFilePath string, it entity.File) error {
	// 断点续合：检查已存在的文件大小
	var resumeOffset int64 = 0
	if stat, err := os.Stat(mergedOutputFilePath); err == nil {
		resumeOffset = stat.Size()
	}

	// 计算需要跳过的块
	var startChunkIndex int = 0   // 计算从哪个块开始合并，实现断点合并
	var accumulatedSize int64 = 0 // 累计已合并的块大小
	for i, chunk := range it.Chunks {
		if accumulatedSize+chunk.Length < resumeOffset {
			accumulatedSize += chunk.Length
			startChunkIndex = i + 1
		} else {
			break
		}
	}

	// 如果所有块都已合并，跳过当前文件
	if startChunkIndex >= len(it.Chunks) {
		// 清理已合并的临时块文件
		for _, chunk := range it.Chunks {
			os.Remove(chunk.OutputFilePath)
		}
		return nil
	}

	// 如果文件不存在则创建，如果存在则打开准备写入
	fw, err := os.OpenFile(mergedOutputFilePath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		slog.WarnContext(ctx, "打开合并目标文件失败", "err", err, "filePath", mergedOutputFilePath)
		return event.ErrDownloadError().Wrap(err)
	}
	defer fw.Close()

	// 将文件指针移动到正确的位置（即已合并内容的末尾）
	if resumeOffset > 0 {
		_, err = fw.Seek(resumeOffset, 0)
		if err != nil {
			slog.WarnContext(ctx, "移动文件指针失败", "err", err, "filePath", mergedOutputFilePath, "offset", resumeOffset)
			return event.ErrDownloadError().Wrap(err)
		}
	}

	// 从断点处开始合并
	for i := startChunkIndex; i < len(it.Chunks); i++ {
		chunk := it.Chunks[i]
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 检查块文件是否存在
		chunkStat, err := os.Stat(chunk.OutputFilePath)
		if os.IsNotExist(err) {
			slog.WarnContext(ctx, "分片文件未找到", "chunkPath", chunk.OutputFilePath, "chunkIndex", chunk.Index)
			return event.ErrDownloadError().Wrap(err)
		}

		// 验证块文件已下载大小
		if chunkStat.Size() == 0 {
			slog.WarnContext(ctx, "分片文件为空", "chunkPath", chunk.OutputFilePath, "chunkIndex", chunk.Index)
			return event.ErrDownloadError().Wrap(err)
		}

		fc, err := os.OpenFile(chunk.OutputFilePath, os.O_RDONLY, os.ModePerm)
		if err != nil {
			slog.WarnContext(ctx, "打开分片文件失败", "err", err, "chunkPath", chunk.OutputFilePath, "chunkIndex", chunk.Index)
			return event.ErrDownloadError().Wrap(err)
		}
		written, err := io.Copy(fw, newContextReader(ctx, fc))
		if err != nil && !errors.Is(err, io.EOF) {
			_ = fc.Close()
			slog.WarnContext(ctx, "合并分片文件失败", "err", err, "chunkIndex", chunk.Index, "chunkPath", chunk.OutputFilePath)
			return event.ErrDownloadError().Wrap(err).Wrap(err)
		}
		_ = fc.Close()
		// 验证写入的字节数
		if written != chunkStat.Size() {
			slog.WarnContext(ctx, "分片文件写入不完整", "chunkIndex", chunk.Index, "expectedBytes", chunkStat.Size(), "writtenBytes", written)
			return event.ErrDownloadError().Wrap(err)
		}

		// 合并完成后立即删除临时块文件，节省磁盘空间
		_ = os.Remove(chunk.OutputFilePath)
	}
	return nil
}
