package httpx

import (
	"github.com/mattn/go-ieproxy"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"time"
)

func GetClient(proxy string) *http.Client {
	return initClient(proxy)
}

func initClient(proxy string) *http.Client {
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}
	switch proxy {
	case "direct", "":
	case "system":
		transport.Proxy = ieproxy.GetProxyFunc()
	default:
		proxyURL, err := url.Parse(proxy)
		if err != nil {
			slog.Warn("parse proxy url failed", "err", err)
		} else {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}
	return &http.Client{
		Transport: transport,
	}
}
