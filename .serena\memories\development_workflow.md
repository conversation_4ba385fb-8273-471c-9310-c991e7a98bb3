# 开发工作流程

## 开发环境要求
- Go 1.24 或更高版本
- FFmpeg 和 FFprobe（用于媒体处理功能）
- Git（版本控制）
- Windows 开发环境

## 典型开发流程

### 1. 功能开发
1. 在 `internal/` 下创建或修改相关模块
2. 如果是新的处理阶段，在 `internal/stage/` 下创建
3. 如果是通用工具，在 `pkg/` 下创建
4. 更新相关的 schema 和 handler

### 2. 测试开发
1. 为新功能编写单元测试（`*_test.go`）
2. 使用表驱动测试模式
3. 确保测试覆盖主要功能路径

### 3. 集成测试
1. 使用 `go run .` 测试完整功能
2. 测试不同类型的输入（HTTP URL、M3U8 等）
3. 验证输出文件的正确性

## 调试技巧
- 使用 `--log-level debug` 获取详细日志
- 检查临时目录中的中间文件
- 使用 Go 的内置调试工具

## 性能优化
- 关注并发下载的性能
- 监控内存使用情况
- 优化 FFmpeg 命令参数

## 发布流程
1. 更新版本号（如需要）
2. 运行完整测试套件
3. 使用 `make all` 构建所有平台版本
4. 使用 GoReleaser 创建发布包