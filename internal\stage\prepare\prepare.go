package prepare

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net"
	"os"
	"path/filepath"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/pkg/transport/stdio"
	"strings"
)

func <PERSON><PERSON>(ctx context.Context, task *entity.Task) (err error) {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}
	taskFilepath := filepath.Join(task.TempDir, constant.TaskFileName) // 如果已经存在，则直接返回
	if _, err := os.Stat(taskFilepath); err == nil {
		return nil
	}
	if err := os.MkdirAll(task.TempDir, os.ModePerm); err != nil {
		return err
	}
	newFiles := make([]entity.File, 0, len(task.Files))
	for _, file := range task.Files {
		fileType := getURLType(file.URL)
		handler := GetChunkHandler(fileType)
		var files []entity.File
		retryCount := 1
		addFileFlag := true
		for {
			files, err = handler(ctx, task, file)
			if err != nil {
				var netErr net.Error
				if errors.As(err, &netErr) && retryCount < constant.NetErrRetryMaxCount {
					slog.WarnContext(ctx, fmt.Sprintf("网络错误，第%d次重试", retryCount), "err", err)
					retryCount++
					continue
				}
				if file.OptionalDownload {
					slog.WarnContext(ctx, "文件预处理失败", "ignoreErr", true, "err", err)
					addFileFlag = false
					break
				}
				return err
			}
			break // 正常执行完成无错误，跳出
		}
		if addFileFlag {
			for i, e := range files {
				files[i].URLType = getURLType(e.URL)
			}
			newFiles = append(newFiles, files...)
		}
	}
	task.Files = newFiles
	data, _ := json.MarshalIndent(task, "", "  ")
	err = os.WriteFile(taskFilepath, data, os.ModePerm)
	if err != nil {
		slog.Warn("保存任务文件失败", "err", err)
		return event.ErrPrepareError().Wrap(err)
	}
	if task.Live {
		stdio.Post(event.TaskLiveDetected(task.ID))
	}
	return nil
}

func getURLType(downloadURL string) entity.URLType {
	if strings.Contains(downloadURL, constant.M3u8Extension) {
		return entity.URLTypeM3u8
	}
	return ""
}
