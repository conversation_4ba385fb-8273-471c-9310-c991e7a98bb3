package downloader

import (
	"context"
	"errors"
	"iter"
	"log/slog"
	"snapfile/internal/entity"
	m3u8 "snapfile/pkg/m3u8"
	"time"
)

func GetChunk(ctx context.Context, lived bool, file entity.File) iter.Seq2[int, entity.Chunk] {
	if lived && file.URLType == entity.URLTypeM3u8 {
		return m3u8LiverChunks(ctx, file)
	} else {
		return func(yield func(int, entity.Chunk) bool) {
			for i, chunk := range file.Chunks {
				select {
				case <-ctx.Done():
					return
				default:
				}
				if !yield(i, chunk) {
					return
				}
			}
		}
	}
}

func m3u8LiverParse(ctx context.Context, file entity.File, cache map[string]struct{}, c chan entity.Chunk) {
	result, err := m3u8.ParseFromURL(ctx, file)
	if err != nil {
		if errors.Is(err, context.Canceled) || errors.Is(err, errorStop) {
			return
		}
		slog.WarnContext(ctx, "解析M3u8文件失败", "err", err)
		return
	}
	if result.M3u8 == nil {
		return
	}
	if result.M3u8.MapURI != "" {
		if _, ok := cache[result.M3u8.MapURI]; !ok {
			downloadURL, err := m3u8.GetSegmentURL(result.URL.String(), result.M3u8.MapURI)
			if err != nil {
				slog.WarnContext(ctx, "获取M3U8直播流下载链接失败", "err", err)
			} else {
				cache[result.M3u8.MapURI] = struct{}{}
				c <- entity.Chunk{
					DownloadURL: downloadURL,
				}
			}
		}
	}
	for _, segment := range result.M3u8.Segments {
		if _, ok := cache[segment.URI]; ok {
			continue
		}
		downloadURL, err := m3u8.GetSegmentURL(result.URL.String(), segment.URI)
		if err != nil {
			slog.WarnContext(ctx, "获取M3U8直播流下载链接失败", "err", err)
			continue
		}
		cache[segment.URI] = struct{}{}
		c <- entity.Chunk{
			Duration:    segment.Duration,
			DownloadURL: downloadURL,
		}
	}
}

func m3u8LiverChunks(ctx context.Context, file entity.File) iter.Seq2[int, entity.Chunk] {
	c := make(chan entity.Chunk, 1024)
	var ticker *time.Ticker
	go func() {
		ticker = time.NewTicker(time.Second * 5)
		cache := make(map[string]struct{})
		m3u8LiverParse(ctx, file, cache, c) //  立马执行第一次录制，不必等待 ticker
		for {
			select {
			case <-ctx.Done():
				close(c)
				ticker.Stop()
				return
			case <-ticker.C:
				m3u8LiverParse(ctx, file, cache, c)
			}
		}
	}()
	return func(yield func(int, entity.Chunk) bool) {
		var i = 0
		for chunk := range c {
			chunk.Index = i
			if !yield(i, chunk) {
				if ticker != nil {
					ticker.Stop()
				}
				return
			}
			i++
		}
	}
}
