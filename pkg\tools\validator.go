package tools

import (
	"errors"
	"github.com/go-playground/validator/v10"
	"reflect"
)

var validate *validator.Validate

func init() {
	validate = validator.New(validator.WithRequiredStructEnabled())
}

func Validate(u interface{}) error {
	err := validate.Struct(u)
	if err != nil {
		return errors.New(processErr(u, err))
	}
	return nil
}

func processErr(u interface{}, err error) string {
	if err == nil { //如果为nil 说明校验通过
		return ""
	}

	var invalid *validator.InvalidValidationError
	if errors.As(err, &invalid) { //如果是输入参数无效，则直接返回输入参数错误
		return "输入参数错误：" + invalid.Error()
	}
	var validationErrs validator.ValidationErrors
	if !errors.As(err, &validationErrs) { //断言是ValidationErrors
		return "输入参数错误：" + invalid.Error()
	}
	for _, validationErr := range validationErrs {
		fieldName := validationErr.Field() //获取是哪个字段不符合格式

		// 获取结构体类型，如果是指针则获取指针指向的类型
		structType := reflect.TypeOf(u)
		if structType.Kind() == reflect.Ptr {
			structType = structType.Elem()
		}

		field, ok := structType.FieldByName(fieldName) //通过反射获取filed
		if ok {
			errorInfo := field.Tag.Get("err") //获取field对应的err tag值
			if errorInfo == "" {
				return invalid.Error()
			}
			return errorInfo //返回错误
		} else {
			return "缺失err"
		}
	}
	return ""
}
