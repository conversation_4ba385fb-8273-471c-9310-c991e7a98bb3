package handler

import (
	"snapfile/pkg/transport"
	"snapfile/pkg/transport/stdio"
)

const (
	EventTypeSayHello              = "say-hello"
	EventTypeStartTask             = "start-task"
	EventTypeDeleteTask            = "delete-task"
	EventTypeStopRecordingLive     = "stop-recording-live"
	EventTypeUpdateMaxDownloadTask = "update-max-download-task"
)

func NewStdioServer() transport.Server {
	server := stdio.NewServer()
	server.Register(EventTypeSayHello, SayHelloHandler)
	server.Register(EventTypeStartTask, StartTaskHandler)
	server.Register(EventTypeDeleteTask, DeleteTaskHandler)
	server.Register(EventTypeStopRecordingLive, StopRecordingLiveHandler)
	server.Register(EventTypeUpdateMaxDownloadTask, UpdateMaxDownloadTaskHandler)
	return server
}
