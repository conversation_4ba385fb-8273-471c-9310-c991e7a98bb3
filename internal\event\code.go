package event

type Code string

const (
	CodeSuccess Code = "success" // 成功
)

// 任务相关事件 2000-2999
const (
	CodeTaskComplete           Code = "task_complete"            // 任务完成
	CodeTaskDownloadProgress   Code = "task_download_progress"   // 更新任务下载进度
	CodeTaskConversionProgress Code = "task_conversion_progress" // 更新任务转换进度
	CodeTaskStarted            Code = "task_started"             // 任务开始
	CodeTaskDeleted            Code = "task_deleted"             // 任务已删除
	CodeTaskStartPrepare       Code = "task_start_prepare"       // 任务开始预处理
	CodeTaskPrepared           Code = "task_prepared"            // 任务预处理完成
	CodeTaskStartDownload      Code = "task_start_download"      // 任务开始下载
	CodeTaskDownloaded         Code = "task_downloaded"          // 任务下载完成
	CodeTaskPendingConversion  Code = "task_pending_conversion"  // 任务等待转换
	CodeTaskStartConversion    Code = "task_start_conversion"    // 任务开始转换
	CodeTaskConverted          Code = "task_converted"           // 任务转换完成
	CodeTaskStartMove          Code = "task_start_move"          // 任务开始移动
	CodeTaskMoved              Code = "task_moved"               // 任务移动完成
	CodeStopRecordingLive      Code = "stop_recording_live"      // 停止录制直播
	CodeTaskPendingDownload    Code = "task_pending_download"    // 等待下载
	CodeTaskLiveDetected       Code = "task_live_detected"       // 检测到任务是直播
)

// 客户端造成的错误 4000-4999
const (
	CodeUnknownEvent       Code = "unknown_event"        // 未知事件
	CodeParamInvalid       Code = "param_invalid"        // 输入参数错误
	CodeTaskAlreadyStarted Code = "task_already_started" // 任务已开始
)

// 服务端造成的错误 5000-5999
const (
	CodeUnknownError             Code = "unknown_error"               // 未知错误
	CodePrepareError             Code = "prepare_error"               // 准备阶段错误
	CodeParseError               Code = "parse_m3u8_error"            // 准备阶段m3u8解析错误
	CodeDownloadError            Code = "download_error"              // 下载阶段错误
	CodeConvertError             Code = "convert_error"               // 转换阶段错误
	CodeMoveError                Code = "move_error"                  // 移动阶段错误
	CodeHttpStatusForbiddenError Code = "http_status_forbidden_error" // http 403错误
	CodeDiskFull                 Code = "disk_full"                   // 磁盘空间已满
	CodeOSPermissionDenied       Code = "os_permission_denied"        // 磁盘没有权限
	CodeFileDownloadError        Code = "file_download_error"         // 文件下载错误
)

func (c Code) Detail() string {
	switch c {
	case CodeSuccess:
		return "成功"
	case CodeTaskComplete:
		return "任务完成"
	case CodeUnknownEvent:
		return "未知事件"
	case CodeTaskAlreadyStarted:
		return "任务已启动"
	case CodeTaskDownloadProgress:
		return "更新下载进度"
	case CodeTaskConversionProgress:
		return "更新转换进度"
	case CodeStopRecordingLive:
		return "停止录制直播"
	case CodeTaskStarted:
		return "任务已启动"
	case CodeUnknownError:
		return "未知错误"
	case CodePrepareError:
		return "预处理错误"
	case CodeParseError:
		return "解析错误"
	case CodeDownloadError:
		return "下载错误"
	case CodeConvertError:
		return "转换错误"
	case CodeMoveError:
		return "移动错误"
	case CodeHttpStatusForbiddenError:
		return "下载链接403错误"
	case CodeTaskPrepared:
		return "任务预处理完成"
	case CodeTaskDownloaded:
		return "任务下载完成"
	case CodeTaskConverted:
		return "任务转换完成"
	case CodeTaskMoved:
		return "任务移动完成"
	case CodeTaskStartPrepare:
		return "任务开始预处理"
	case CodeTaskStartDownload:
		return "任务开始下载"
	case CodeTaskStartConversion:
		return "任务开始转换"
	case CodeTaskStartMove:
		return "任务开始移动"
	case CodeTaskDeleted:
		return "任务已删除"
	case CodeTaskPendingConversion:
		return "任务等待转换"
	case CodeTaskPendingDownload:
		return "等待下载"
	case CodeTaskLiveDetected:
		return "检测到直播任务"
	case CodeDiskFull:
		return "磁盘已满"
	case CodeOSPermissionDenied:
		return "磁盘权限不足"
	case CodeParamInvalid:
		return "输入参数错误"
	case CodeFileDownloadError:
		return "文件下载错误"
	}
	return "未知"
}
