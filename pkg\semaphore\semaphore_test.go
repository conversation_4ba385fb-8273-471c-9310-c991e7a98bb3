package semaphore

import (
	"context"
	"testing"
	"time"
)

func TestSemaphore_BasicOperation(t *testing.T) {
	sem := NewSemaphore(2)
	ctx := context.Background()

	// 测试获取和释放
	err := sem.Acquire(ctx)
	if err != nil {
		t.Fatalf("首次获取信号量失败: %v", err)
	}

	err = sem.Acquire(ctx)
	if err != nil {
		t.Fatalf("第二次获取信号量失败: %v", err)
	}

	// 此时信号量应该已满
	capacity, acquired, available := sem.GetStats()
	if capacity != 2 || acquired != 2 || available != 0 {
		t.<PERSON><PERSON>rf("信号量状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}

	// 释放一个
	sem.Release()
	capacity, acquired, available = sem.GetStats()
	if capacity != 2 || acquired != 1 || available != 1 {
		t.<PERSON><PERSON><PERSON>("释放后信号量状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}

	// 释放另一个
	sem.Release()
	capacity, acquired, available = sem.GetStats()
	if capacity != 2 || acquired != 0 || available != 2 {
		t.Errorf("全部释放后信号量状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}
}

func TestSemaphore_SetCapacity(t *testing.T) {
	sem := NewSemaphore(2)
	ctx := context.Background()

	// 获取两个permits
	sem.Acquire(ctx)
	sem.Acquire(ctx)

	// 扩容到4
	sem.SetCapacity(4)
	capacity, acquired, available := sem.GetStats()
	if capacity != 4 || acquired != 2 || available != 2 {
		t.Errorf("扩容后状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}

	// 减容到1
	sem.SetCapacity(1)
	capacity, acquired, available = sem.GetStats()
	if capacity != 1 || acquired != 2 {
		t.Errorf("减容后状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}

	// 释放一个
	sem.Release()
	capacity, acquired, available = sem.GetStats()
	if capacity != 1 || acquired != 1 || available != 0 {
		t.Errorf("减容后释放一个的状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}

	// 释放另一个
	sem.Release()
	capacity, acquired, available = sem.GetStats()
	if capacity != 1 || acquired != 0 || available != 1 {
		t.Errorf("减容后全部释放的状态错误: capacity=%d, acquired=%d, available=%d", capacity, acquired, available)
	}
}

func TestSemaphore_Concurrency(t *testing.T) {
	sem := NewSemaphore(1)
	ctx := context.Background()

	go func() {
		if err := sem.Acquire(ctx); err != nil {
			t.Error(err)
			return
		}
		t.Log("1拿到了")
		time.Sleep(2 * time.Second) // 持有信号量一段时间
		sem.Release()
	}()
	go func() {
		if err := sem.Acquire(ctx); err != nil {
			t.Error(err)
			return
		}
		t.Log("2拿到了")
		time.Sleep(2 * time.Second) // 持有信号量一段时间
		sem.Release()
	}()
	time.Sleep(5 * time.Second)
	sem.Acquire(ctx)
	defer sem.Release()
}
