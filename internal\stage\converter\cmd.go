package converter

import (
	"context"
	_ "embed"
	"fmt"
	"log/slog"
	"os/exec"
	"path"
	"slices"
	"snapfile/internal/config"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/pkg/ffmpeg"
	"snapfile/pkg/language"
	"snapfile/pkg/tools"
	"strconv"
	"strings"

	"github.com/gabriel-vasile/mimetype"
)

type stream struct {
	Path      string
	Type      string // "video", "audio", "subtitle", "image", "other"
	CodecName string
	Duration  int64  // 只有音视频文件有
	Language  string // 只有字幕文件有
}

func getLanguageCodeByFilename(filename string) string {
	return tools.GetLanguage(filename)
}

// 使用读取头文件的方式判断图片文件
func getImageNativeStream(filePath string) (*stream, bool) {
	mt, err := mimetype.DetectFile(filePath)
	if err != nil {
		slog.Warn("mimetype解析文件失败", "err", err, "filePath", filePath)
		return nil, false
	}
	if mt.String() == "" {
		return nil, false
	}
	mts := strings.Split(mt.String(), "/")
	if len(mts) < 2 {
		return nil, false
	}
	if mts[0] != "image" {
		return nil, false
	}
	return &stream{
		Path:      filePath,
		Type:      constant.FileTypeImage,
		CodecName: mts[1],
	}, true
}

func GetFileStreams(ctx context.Context, filePath string) ([]stream, error) {
	if ste, ok := getImageNativeStream(filePath); ok {
		return []stream{*ste}, nil
	}
	fp, err := ffmpeg.GetFFProbeRes(ctx, filePath)
	if err != nil {
		slog.Warn("执行FFprobe获取文件流失败", "err", err, "filePath", filePath)
		return nil, event.ErrConvertError().Wrap(err)
	}
	if len(fp.Streams) == 0 {
		return []stream{
			{
				Path:     filePath,
				Type:     constant.FileTypeOther,
				Language: getLanguageCodeByFilename(path.Base(filePath)),
			},
		}, nil
	}
	var fs []stream
	for _, fStream := range fp.Streams {
		duration, _ := strconv.ParseFloat(fStream.Duration, 64)
		if duration > 0 {
			duration = duration * 1000000
		}
		var f = stream{
			Path:      filePath,
			Duration:  int64(duration),
			CodecName: fStream.CodecName,
		}
		switch fStream.CodecType {
		case constant.FileTypeVideo:
			switch fStream.CodecName {
			case "mjpeg", "jpeg", "png", "webp", "gif", "bmp", "tiff", "heif", "ico", "svg":
				f.Type = constant.FileTypeImage
			default:
				f.Type = fStream.CodecType
				f.Language = getLanguageCodeByFilename(path.Base(filePath))
			}
		case constant.FileTypeAudio:
			f.Type = fStream.CodecType
			f.Language = getLanguageCodeByFilename(path.Base(filePath))
		case constant.FileTypeSubtitle:
			f.Type = fStream.CodecType
			f.Language = getLanguageCodeByFilename(path.Base(filePath))
		default:
			continue
		}
		fs = append(fs, f)
	}
	return fs, nil
}

// 合并为音频，优先从音频中提取，如果音频不存在，则从视频中提取
func buildOutputAudioCmd(ctx context.Context, mediaStreams []stream, convertedOutputFilepath string, outputFormat entity.OutputFormat) (*exec.Cmd, []stream, bool) {
	otherFiles := make([]stream, 0)
	if !hasType(mediaStreams, constant.FileTypeAudio) {
		return nil, mediaStreams, false
	}
	builder := ffmpeg.NewBuilder()
	builder.Progress()
	var ok bool
	i := 0
	for _, stream := range mediaStreams {
		switch stream.Type {
		case constant.FileTypeVideo: // 不处理视频
		case constant.FileTypeAudio:
			ok = true
			builder.InputVideo(stream.Path)
			builder.AddArgs("-map", fmt.Sprintf("%d:a:0", i))
			switch outputFormat {
			case entity.AudioFormatMP3:
				if needCodec([]string{stream.CodecName}, entity.AudioFormatMP3) {
					builder.AddArgs("-c:a", "libmp3lame")
				} else {
					builder.AddArgs("-c:a", "copy")
				}
			case entity.AudioFormatM4A:
				if needCodec([]string{stream.CodecName}, entity.AudioFormatM4A) {
					builder.AddArgs("-c:a", "aac")
				} else {
					builder.AddArgs("-c:a", "copy")
				}
			case entity.AudioFormatOGG:
				if needCodec([]string{stream.CodecName}, entity.AudioFormatOGG) {
					builder.AddArgs("-c:a", "libvorbis")
				} else {
					builder.AddArgs("-c:a", "copy")
				}
				builder.AddArgs("-map_metadata", "-1")
			}
			name := convertedOutputFilepath + "." + outputFormat
			if stream.Language != "" && language.GetISOLanguageCode(stream.Language) != "und" {
				name = tools.SetLanguage(name, language.GetLanguageName(stream.Language))
			}
			builder.AddArgs(name)
			i++
		case constant.FileTypeSubtitle:
			builder.AddArgs("-i", stream.Path)
			name := fmt.Sprintf("%s.srt", convertedOutputFilepath)
			if stream.Language != "" && language.GetISOLanguageCode(stream.Language) != "und" {
				name = tools.SetLanguage(name, language.GetLanguageName(stream.Language))
			}
			builder.AddArgs("-map", fmt.Sprintf("%d:s:0", i), name)
			i++
		default:
			otherFiles = append(otherFiles, stream)
		}
	}
	return exec.CommandContext(ctx, config.GlobalConfig.FFmpegPath, builder.GetArgs()...), otherFiles, ok
}

func hasType(mediaStreams []stream, _type string) bool {
	for _, mediaStream := range mediaStreams {
		if mediaStream.Type == _type {
			return true
		}
	}
	return false
}

var codecGroups = map[string]string{
	// 视频
	"h264": "h264", "avc1": "h264",
	"hevc": "hevc", "h265": "hevc",
	"vp9": "vp9", "vp09": "vp9",
	"av1": "av1", "av01": "av1",

	// 音频
	"aac":    "aac",
	"mp3":    "mp3",
	"vorbis": "vorbis",
	"opus":   "opus",
	"flac":   "flac",
	"alac":   "alac",
}

// normalizeCodec 把别名归一化
func normalizeCodec(codec string) string {
	codec = strings.ToLower(codec)
	if normalized, ok := codecGroups[codec]; ok {
		return normalized
	}
	return codec
}

// needCodec 判断是否需要重新编码
func needCodec(inputCodecs []string, ext string) bool {
	ext = strings.ToLower(ext)

	for _, codec := range inputCodecs {
		normalized := normalizeCodec(codec)

		switch ext {
		case entity.VideoFormatMP4:
			if slices.Contains([]string{"h264", "hevc", "aac", "av1", "vp9"}, normalized) {
				return false
			}
		case entity.VideoFormatMKV:
			if slices.Contains([]string{"h264", "hevc", "aac", "av1", "vp9", "mp3", "opus", "vorbis", "flac", "alac"}, normalized) {
				return false
			}
		case entity.AudioFormatMP3:
			if normalized == "mp3" {
				return false
			}
		case entity.AudioFormatM4A:
			if slices.Contains([]string{"aac", "alac"}, normalized) {
				return false
			}
		case entity.AudioFormatOGG:
			if slices.Contains([]string{"vorbis", "opus", "flac"}, normalized) {
				return false
			}
		default:
			return true // 不支持的容器
		}
	}

	// 没有匹配可直接封装
	return true
}

// 合并为视频
func buildOutputVideoCmd(ctx context.Context, mediaStreams []stream, convertedOutputFilepath string, outputFormat entity.OutputFormat, embeddedSubtitles bool) (*exec.Cmd, []stream, bool) {
	otherFiles := make([]stream, 0)
	if !hasType(mediaStreams, constant.FileTypeVideo) {
		return nil, mediaStreams, false
	}
	builder := ffmpeg.NewBuilder()
	builder.Progress()
	subtitleIndex := 0
	audioIndex := 0
	ok := false      // 是否需要转换
	streamIndex := 0 // 流在整个命令中的索引
	videoInputCodecs := make([]string, 0)
	audioInputCodecs := make([]string, 0)
	for _, stream := range mediaStreams {
		switch stream.Type {
		case constant.FileTypeVideo:
			if !strings.Contains(stream.Path, "first") {
				continue
			}
			ok = true // 需要转换
			builder.InputFilePath(stream.Path)
			builder.AddArgs("-map", fmt.Sprintf("%d:v:0", streamIndex))
			videoInputCodecs = append(videoInputCodecs, stream.CodecName)
			streamIndex++
		case constant.FileTypeAudio:
			builder.InputFilePath(stream.Path)
			builder.AddArgs("-map", fmt.Sprintf("%d:a:0", streamIndex))
			if stream.Language != "" {
				builder.AddArgs(fmt.Sprintf("-metadata:s:a:%d", audioIndex), fmt.Sprintf("language=%s", language.GetISOLanguageCode(stream.Language)))
				builder.AddArgs(fmt.Sprintf("-metadata:s:a:%d", audioIndex), fmt.Sprintf("title=%s", language.GetLanguageName(stream.Language)))
			}
			audioInputCodecs = append(audioInputCodecs, stream.CodecName)
			audioIndex++
			streamIndex++
		case constant.FileTypeSubtitle:
			if embeddedSubtitles {
				builder.InputFilePath(stream.Path)
				builder.AddArgs("-map", fmt.Sprintf("%d:s:0", streamIndex))
				if stream.Language != "" {
					builder.AddArgs(fmt.Sprintf("-metadata:s:s:%d", subtitleIndex), fmt.Sprintf("language=%s", language.GetISOLanguageCode(stream.Language)))
					builder.AddArgs(fmt.Sprintf("-metadata:s:s:%d", subtitleIndex), fmt.Sprintf("title=%s", language.GetLanguageName(stream.Language)))
				}
				subtitleIndex++
				switch outputFormat {
				case entity.VideoFormatMP4:
					builder.AddArgs("-c:s", "mov_text")
					builder.AddArgs("-strict", "experimental")
				case entity.VideoFormatMKV:
					ext := path.Ext(stream.Path)
					switch ext {
					case ".srt":
						builder.AddArgs("-c:s", "srt")
					case ".ass", ".ssa":
						builder.AddArgs("-c:s", "ass")
					case ".vtt":
						builder.AddArgs("-c:s", "webvtt")
					default:
					}
				}
				streamIndex++
			}
		default:
			otherFiles = append(otherFiles, stream)
		}
	}
	subtitleArgs := make([]string, 0)
	if !embeddedSubtitles { // 如果不需要嵌入字幕，则需要单独处理字幕, 因为输入的流顺序不一致
		for _, stream := range mediaStreams {
			if stream.Type == constant.FileTypeSubtitle {
				subtitleArgs = append(subtitleArgs, "-i", stream.Path)
				name := fmt.Sprintf("%s.srt", convertedOutputFilepath)
				if stream.Language != "" && language.GetISOLanguageCode(stream.Language) != "und" {
					name = tools.SetLanguage(name, language.GetLanguageName(stream.Language))
				}
				subtitleArgs = append(subtitleArgs, "-map", fmt.Sprintf("%d:s:0", streamIndex), "-c:s", "srt", name)
				streamIndex++
			}
		}
	}
	if outputFormat == entity.VideoFormatMP4 {
		builder.AddArgs("-movflags", "+faststart")
	}
	if needCodec(videoInputCodecs, outputFormat) {
		builder.AddArgs("-c:v", "libx264")
	} else {
		builder.AddArgs("-c:v", "copy")
	}
	if needCodec(audioInputCodecs, outputFormat) {
		builder.AddArgs("-c:a", "aac")
	} else {
		builder.AddArgs("-c:a", "copy")
	}
	builder.OutputFilePath(convertedOutputFilepath + "." + outputFormat)
	args := append(builder.GetArgs(), subtitleArgs...)
	return exec.CommandContext(ctx, config.GlobalConfig.FFmpegPath, args...), otherFiles, ok
}

func buildOutputSubtitleCmd(ctx context.Context, mediaStreams []stream, convertedOutputFilepath string) (*exec.Cmd, []stream, bool) {
	otherFiles := make([]stream, 0)
	if !hasType(mediaStreams, constant.FileTypeSubtitle) {
		return nil, mediaStreams, false
	}
	builder := ffmpeg.NewBuilder()
	builder.Progress()
	ok := false
	for i, stream := range mediaStreams {
		if stream.Type != constant.FileTypeSubtitle {
			otherFiles = append(otherFiles, stream)
			continue
		}
		ok = true
		builder.AddArgs("-i", stream.Path)
		name := fmt.Sprintf("%s.srt", convertedOutputFilepath)
		if stream.Language != "" && language.GetISOLanguageCode(stream.Language) != "und" {
			name = tools.SetLanguage(name, language.GetLanguageName(stream.Language))
		}
		builder.AddArgs("-map", fmt.Sprintf("%d:s:0", i), "-c:s", "srt", name)
	}
	return exec.CommandContext(ctx, config.GlobalConfig.FFmpegPath, builder.GetArgs()...), otherFiles, ok
}
