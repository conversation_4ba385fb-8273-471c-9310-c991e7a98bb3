package prepare

import (
	"context"
	"snapfile/internal/entity"
	"testing"
)

func TestHandler(t *testing.T) {
	err := Handler(context.Background(), &entity.Task{
		ID:        "123",
		Name:      "测试文件",
		OutputDir: "",
		TempDir:   ".snapfile/123",
		Files: []entity.File{
			{
				URL:     "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_adv_example_hevc/master.m3u8?88862",
				URLType: "m3u8",
				Headers: map[string]string{
					"Referer": "https://v3-web.douyinvod.com",
				},
			},
		},
		EmbeddedSubtitle:  true,
		OutputType:        "video",
		OutputVideoFormat: "mp4",
		OutputAudioFormat: "mp3",
		DownloadProgressCallback: func(stage string, total int64, done int64) {
			t.Logf("stage: %s, total: %d, done: %d\n", stage, total, done)
		},
	})
	if err != nil {
		t.<PERSON><PERSON>r(err)
	}
}
