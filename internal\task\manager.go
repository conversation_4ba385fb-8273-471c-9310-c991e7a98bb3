package task

import (
	"context"
	"errors"
	"snapfile/internal/config"
	"snapfile/internal/constant"
	"snapfile/internal/entity"
	"snapfile/internal/event"
	"snapfile/internal/stage"
	"snapfile/pkg/diskerror"
	"snapfile/pkg/semaphore"
	"snapfile/pkg/tools"
	"snapfile/pkg/transport/stdio"
	"sync"
)

type Context struct {
	Ctx    context.Context
	Cancel context.CancelFunc
}

type Manager struct {
	tasks       sync.Map // map[string]Context
	downloadSem *semaphore.Semaphore
	convertDem  *semaphore.Semaphore
}

var manager *Manager

func init() {
	manager = &Manager{
		tasks:       sync.Map{},
		downloadSem: semaphore.NewSemaphore(config.GlobalConfig.MaxDownloadTask),
		convertDem:  semaphore.NewSemaphore(1),
	}
}

func AddTask(taskID string, task entity.Task) {
	if Get(taskID) != nil {
		return
	}
	ctx := context.Background()
	ctx = tools.SetValue(ctx, taskID, task)
	ctx = tools.SetValue(ctx, constant.CtxProxyKey, task.Proxy)
	ctx = tools.SetValue(ctx, constant.CtxDownloadSem<PERSON><PERSON>, manager.downloadSem)
	ctx = tools.SetValue(ctx, constant.CtxConvertSemKey, manager.convertDem)
	ctx = tools.SetTaskID(ctx, taskID)
	ctx, cancel := context.WithCancel(ctx)
	taskCtx := &Context{
		Ctx:    ctx,
		Cancel: cancel,
	}
	manager.tasks.Store(taskID, taskCtx)
	task.DownloadProgressCallback = func(stage string, total int64, done int64, speed int64, remainingTime int64) {
		stdio.Post(event.TaskDownloadProgress(taskID, total, done, speed, remainingTime))
	}
	task.ConvertProgressCallback = func(stage string, total int64, done int64, speed int64, remainingTime int64) {
		stdio.Post(event.TaskConvertProgress(taskID, total, done, speed, remainingTime))
	}
	go func() {
		defer DelTask(taskID)
		err := stage.RunStages(ctx, &task)
		if err != nil {
			if errors.Is(err, context.Canceled) { // 取消任务(删除任务)
				stdio.Post(event.TaskDeleted(taskID))
			} else if diskerror.IsDiskFull(err) {
				stdio.Post(event.NewErrOutput(event.ErrDiskFullError(), event.WithTaskID(taskID)))
			} else if diskerror.IsPermission(err) {
				stdio.Post(event.NewErrOutput(event.ErrOSPermissionDeniedError(), event.WithTaskID(taskID)))
			} else {
				stdio.Post(event.NewErrOutput(err, event.WithTaskID(taskID)))
			}
			return
		}
		stdio.Post(event.TaskComplete(taskID, task.FinalizedFilePaths))
	}()
}

func DelTask(taskID string) {
	val, ok := manager.tasks.LoadAndDelete(taskID)
	if !ok {
		return
	}
	if ctx, ok := val.(*Context); ok {
		ctx.Cancel()
	}
}

func Get(taskID string) *Context {
	if val, ok := manager.tasks.Load(taskID); ok {
		return val.(*Context)
	}
	return nil
}

func StopRecordingLive(taskID string) {
	ctx := Get(taskID)
	if ctx == nil {
		return
	}
	cancel := tools.GetValue(ctx.Ctx, constant.CtxLiveCancelKeyPrefix+taskID)
	if cancel != nil {
		cancel.(context.CancelFunc)()
	}
}

func UpdateMaxDownloadTask(limit int) {
	config.GlobalConfig.MaxDownloadTask = limit
	manager.downloadSem.SetCapacity(limit)
}
