package shard

import (
	"errors"
	"fmt"
)

// ByteSize 模仿 time.Duration 定义，便于表达文件大小常量。
type ByteSize int64

const (
	_           = iota
	KB ByteSize = 1 << (10 * iota)
	MB
	GB
	TB
)

func UnitFormat(size ByteSize) string {
	switch {
	case size >= TB:
		return fmt.Sprintf("%.2fTB", float64(size)/float64(TB))
	case size >= GB:
		return fmt.Sprintf("%.2fGB", float64(size)/float64(GB))
	case size >= MB:
		return fmt.Sprintf("%.2fMB", float64(size)/float64(MB))
	case size >= KB:
		return fmt.Sprintf("%.2fKB", float64(size)/float64(KB))
	default:
		return fmt.Sprintf("%dB", size)
	}
}

// Chunk 表示一个文件片段：起始偏移与长度。
type Chunk struct {
	Index  int   `json:"index,omitempty"`  // 第几块，从 0 开始
	Offset int64 `json:"offset,omitempty"` // 在原文件中的起始字节
	Length int64 `json:"length,omitempty"` // 该块长度（字节）
}

// SplitBySize 按照给定块大小 (chunkSize) 切分 totalSize。
// 最后一块可能小于 chunkSize。
func SplitBySize(totalSize int64, chunkSize ByteSize) ([]Chunk, error) {
	if totalSize <= 0 {
		return nil, errors.New("totalSize must be > 0")
	}
	if chunkSize <= 0 {
		return nil, errors.New("chunkSize must be > 0")
	}

	var chunks []Chunk
	for offset, idx := int64(0), 0; offset < totalSize; offset += int64(chunkSize) {
		length := int64(chunkSize)
		if offset+length > totalSize {
			length = totalSize - offset // 处理最后一块
		}
		chunks = append(chunks, Chunk{
			Index:  idx,
			Offset: offset,
			Length: length,
		})
		idx++
	}
	return chunks, nil
}

// AutoChunkSize 根据文件大小自动选择合适的分片大小。
// 档位规则：
//  1. <100 MB, 每片 5 MB
//  2. <200 MB, 每片 10 MB
//  3. <2000MB, 每片 20 MB
//  4. >2000MB, 每片 50 MB
func AutoChunkSize(totalSize int64) ByteSize {
	switch {
	case totalSize < int64(100*MB):
		return 5 * MB
	case totalSize < int64(200*MB):
		return 10 * MB
	case totalSize < int64(2000*MB):
		return 20 * MB
	default:
		return 50 * MB
	}
}
