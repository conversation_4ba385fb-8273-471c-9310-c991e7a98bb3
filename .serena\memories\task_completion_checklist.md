# 任务完成检查清单

## 代码开发完成后的必要步骤

### 1. 代码质量检查
- [ ] 运行 `go fmt ./...` 格式化代码
- [ ] 运行 `go vet ./...` 进行静态分析
- [ ] 确保代码遵循项目命名约定和风格

### 2. 测试
- [ ] 为新功能编写单元测试
- [ ] 运行 `go test ./...` 确保所有测试通过
- [ ] 检查测试覆盖率是否合理

### 3. 依赖管理
- [ ] 运行 `go mod tidy` 整理依赖
- [ ] 确认 go.mod 和 go.sum 文件正确更新

### 4. 构建验证
- [ ] 运行 `go build .` 确保项目可以正常构建
- [ ] 测试关键功能是否正常工作

### 5. 文档更新
- [ ] 更新 README.md（如有必要）
- [ ] 更新相关的代码注释
- [ ] 更新项目文档（如有重大变更）

### 6. 提交前检查
- [ ] 检查 git status，确认要提交的文件
- [ ] 编写清晰的提交信息
- [ ] 考虑是否需要更新版本号

## 特殊情况检查
- 如果修改了 FFmpeg 相关功能，确保在有 FFmpeg 环境下测试
- 如果修改了下载功能，测试不同类型的 URL（HTTP、M3U8等）
- 如果修改了并发逻辑，测试高并发场景